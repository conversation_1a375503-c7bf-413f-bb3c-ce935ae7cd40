<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<div class="lang">4545</div>

<body>
    <script src="js/jquery-3.2.1.min.js"></script>
    <script src="js/jquery.i18n.properties-1.0.9.js"></script>
    <script>
        //语言选择
        languages = 'en';
        var language_pack = {
            now_lang: 0, // 0:ch,1:en
            loadProperties: function(new_lang, titles) {
                var self = this;
                var tmp_lang = '';
                if (new_lang == 0) {
                    tmp_lang = 'zh';
                    $('body').removeClass('en').addClass('zh');
                } else {
                    tmp_lang = 'en';
                    $('body').removeClass('zh').addClass('en');
                }
                $.i18n.properties({ //加载资浏览器语言对应的资源文件
                    name: 'teambox', //资源文件名称
                    path: '/language/', //资源文件路径
                    language: tmp_lang,
                    cache: false,
                    mode: 'map', //用Map的方式使用资源文件中的值
                    callback: function() { //加载成功后设置显示内容
                        for (var i in $.i18n.map) {
                            $('[data-lang="' + i + '"]').text($.i18n.map[i]);
                        }
                        document.title = $.i18n.map[titles];
                        //document.title = 'bigmom';
                    }
                });
                self.now_lang = new_lang;
            }
        }
        if (languages) {
            language_pack.loadProperties(1, 'string_lang_basketball_t');
        }
        $('.lang').html($.i18n.prop('string_lang_basketball_t'))
    </script>
</body>

</html>