
<!DOCTYPE html>
<html>
 
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>Document</title>
	<style>
		html,
		body {
			padding: 0;
			margin: 0;
		}
 
		::-webkit-scrollbar {
			display: none;
		}
		li:first-child {
			border-top: 1px solid rgb(169, 185, 180);
		}
		li {
			border-bottom: 1px solid rgb(169, 185, 180);
		}
	</style>
</head>
 
<body>
	<ul id="scrollUl" style="box-sizing: border-box;width: 80px;height: 100px;list-style: none;overflow-x: hidden;overflow-y: auto;border: 1px solid rgb(169, 185, 180);padding: 40px 0;margin: 100px auto;">
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">1</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">2</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">3</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">4</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">5</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">6</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">7</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">8</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">9</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">10</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">11</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">12</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">13</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">14</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">15</label>
		</li>
		<li class="scrollLi">
			<input class="scrollRadio" type="radio" name="years" value="1">
			<label for="">16</label>
		</li>
	</ul>
	<script>
		var my = {
			scroll: (id, name) => {
				// 滚动事件
				var ul = document.getElementById(id);
				var change = "";
				var size = document.getElementsByClassName("scrollLi").length;
				// ul.addEventListener("scroll", function () {
				// 	var num = (ul.scrollTop + ul.clientHeight - 80) / (ul.scrollHeight - 80) * size;
				// 	change = num;
				// 	setTimeout(function () {
				// 		if (change === num) {
				// 			num = Math.ceil(num) - 1;
				// 			document.getElementsByName(name)[num].checked = true;
				// 			var distance = num / size * (ul.scrollHeight - 80) - ul.scrollTop;
				// 			for (var i = 0; i < distance; i++) {
				// 				setTimeout(function () {
				// 					ul.scrollTop = ul.scrollTop + 1 * distance / Math.abs(distance);
				// 				}, 100);
				// 			}
				// 		}
				// 	}, 500);
				// });
				// 点击事件
				
				var li = document.getElementsByClassName("scrollLi");
				for (var n = 0; n < li.length; n++) {
					li[n].setAttribute("index", n);
					li[n].addEventListener("click", function () {
						var nav = this.getAttribute("index");
						// var distance = nav / size * (ul.scrollHeight - 80) - ul.scrollTop;
						// for (var i = 0; i < distance; i++) {
						// 	setTimeout(function () {
						// 		ul.scrollTop = ul.scrollTop + 1 * distance / Math.abs(distance);
						// 	}, 100);
						// }
						
					});
				}

			}
		};
		my.scroll("scrollUl", "years"); //分别为ul的id，和input：radio的name值
	</script>
</body>
 
