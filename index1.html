<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>单子</title>
	<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no"/>
	<meta name="apple-mobile-web-app-capable" content="yes"/>
	<meta name="apple-mobile-web-app-status-bar-style" content="black"/>
	<script type="text/javascript" src="js/flexible.debug.js"></script>
    <script type="text/javascript" src="js/flexible_css.debug.js"></script>
	<link rel="stylesheet" type="text/css" href="css/index.css">
</head>
<body>
<div id="wrap_box">
	<input type="text" name="test" class="get" id="cl"><button class="des">确定</button><br><br>
	<input type="text" name="" class="bds"><br>
	<input type="text" name="" class="bds"><br>
	<input type="text" name="" class="bds"><br>
	<input type="text" name="" class="bds"><br>
</div>
<script type="text/javascript" src="js/jquery-3.2.1.min.js"></script>
<!-- <script type="text/javascript" src="js/index1.js"></script> -->
<script type="text/javascript">
var dataMin=Math.ceil(2000/60/2);
console.log(dataMin);
var a=(Math.round(dataMin*10)/10).toFixed(1);
console.log(a);
</script>
</body>
</html>