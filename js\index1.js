
	// $('.des').click(function(){
	// 	var abs=$(".get").val();
	// 	console.log(abs);
	// 	$('.bds').val(abs);
	// })
var arr = [['122111',1],['122111',1],['12',2],['12',1]];

var contains = function(src, key) {
	// console.log(src.find(a => a[0] === key));
    return src.find(a => a[0] === key);
};

var unduplicated = arr.reduce((p, item) => {
	console.log(p);
    let o = contains(p, item[0]);
    o ? o[1] += item[1] : p.push(item);
    return p;
}, []);

console.log(unduplicated); 

