var uploadedDataURL = "./json/na.json";
// var bg = '<div class="bg-tooltip"></div>'
var myChart = echarts.init(document.getElementsByClassName('nanshan')[0]);



var uploadnanshan = './json/440305.geoJson';

$.getJSON(uploadnanshan, function(geonanshanJson) {
    echarts.registerMap('ns', geonanshanJson);
    $.getJSON(uploadedDataURL, function(geoJson) {
        echarts.registerMap('nanshan', geoJson);
        let geoCoordMap = {
            '塘朗': [113.999719, 22.603784],
            '南头': [113.91171099999996, 22.551691],
            '南山': [113.88559699999999, 22.498413],
            '西丽': [113.945385, 22.622042],
            '沙河': [113.9768, 22.5362],
            '蛇口': [113.9235, 22.4863],
            '招商': [113.90036699999993, 22.480851],
            '高新': [113.93729200000007, 22.545173],
            '粤海': [113.92808299999999, 22.516687],
            '桃源': [113.96945000000004, 22.567461],
            '深湾': [113.93860400000004, 22.502813]

        }
        var data = [
            { name: '塘朗', value: 89 },
            { name: '南头', value: 39 },
            { name: '南山', value: 350 },
            { name: '西丽', value: 89 },
            { name: '沙河', value: 89 },
            { name: '蛇口', value: 52 },
            { name: '招商', value: 250 },
            { name: '高新', value: 82 },
            { name: '粤海', value: 99 },
            { name: '桃源', value: 39 },
            { name: '深湾', value: 60 },
        ];
        var max = 480,
            min = 9; // todo 
        var maxSize4Pin = 100,
            minSize4Pin = 20;

        var convertData = function(data) {
            //console.log(data)
            var res = [];
            for (var i = 0; i < data.length; i++) {
                var geoCoord = geoCoordMap[data[i].name];
                if (geoCoord) {
                    res.push({
                        name: data[i].name,
                        //value: data[i].value,
                        value: geoCoord.concat(data[i].value)
                    });
                }
            }
            console.log(res)
            return res;
        };
        option = {
            backgroundColor: '#013954',

            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    if (typeof(params.value)[2] == "undefined") {
                        return params.name + ' : ' + params.value;
                    } else {
                        return params.name + ' : ' + params.value[2];
                    }
                }
            },

            visualMap: {
                show: false,
                min: 0,
                max: 500,
                left: 'left',
                top: 'bottom',
                text: ['高', '低'], // 文本，默认为数值文本
                calculable: true,
                seriesIndex: [1],
                inRange: {
                    // color: ['#3B5077', '#031525'] // 蓝黑
                    // color: ['#ffc0cb', '#800080'] // 红紫
                    // color: ['#3C3B3F', '#605C3C'] // 黑绿
                    //color: ['#0f0c29', '#302b63', '#24243e'] // 黑紫黑
                    // color: ['#23074d', '#cc5333'] // 紫红
                    // color: ['#00467F', '#A5CC82'] // 蓝绿
                    color: ['#1488CC', '#2B32B2'] // 浅蓝
                        // color: ['#00467F', '#A5CC82'] // 蓝绿
                        // color: ['#00467F', '#A5CC82'] // 蓝绿
                        // color: ['#00467F', '#A5CC82'] // 蓝绿
                        // color: ['#00467F', '#A5CC82'] // 蓝绿

                }
            },
            // toolbox: {
            //     show: true,
            //     orient: 'vertical',
            //     left: 'right',
            //     top: 'center',
            //     feature: {
            //             dataView: {readOnly: false},
            //             restore: {},
            //             saveAsImage: {}
            //             }
            // },
            geo: {
                // show: true,
                // zoom: 1.1,
                map: 'nanshan',
                aspectScale: 0.75,
                layoutCenter: ['50%', '50.5%'],
                layoutSize: '100%',
                silent: true,
                roam: false,
                z: 0,
                itemStyle: {
                    normal: {
                        areaColor: {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 500,
                            colorStops: [{
                                    offset: 0,
                                    color: "rgba(3,27,78,0.75)", // 0% 处的颜色
                                },
                                {
                                    offset: 1,
                                    color: "rgba(58,149,253,0.75)", // 50% 处的颜色
                                },
                            ],
                            global: true, // 缺省为 false
                        },

                        shadowColor: 'rgba(0, 0, 0, 1)',
                        shadowBlur: 0,
                        shadowOffsetX: 0,
                        shadowOffsetY: 10,
                        borderColor: 'rgba(0, 0, 0, 0.7)',
                        borderWidth: 0.5,
                    },
                    emphasis: {
                        areaColor: '#2AB8FF',
                        borderWidth: 1,
                        color: 'green',
                        label: {
                            show: false,
                        },
                    },
                },
            },
            series: [{
                    name: 'credit_pm2.5',
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    data: convertData(data),
                    symbolSize: function(val) {
                        return val[2] / 10;
                    },
                    label: {
                        normal: {
                            formatter: '{b}',
                            position: 'right',
                            show: true
                        },
                        emphasis: {
                            show: true
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: '#05C3F9'
                        }
                    }
                },
                {
                    type: 'map',
                    map: 'nanshan',
                    roam: true,
                    label: {
                        normal: {
                            show: false,
                            textStyle: {
                                color: '#fff',
                            },
                        },
                        emphasis: {
                            textStyle: {
                                color: '#fff',
                            },
                        },
                    },
                    itemStyle: {
                        normal: {
                            borderColor: '#2ab8ff',
                            borderWidth: 1,
                            areaColor: {
                                type: "linear",
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 500,
                                colorStops: [{
                                        offset: 0,
                                        color: "rgba(3,27,78,0.75)", // 0% 处的颜色
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(58,149,253,0.75)", // 50% 处的颜色
                                    },
                                ],
                                global: true, // 缺省为 false
                            },
                            shadowColor: 'rgba(0, 0, 0, 0.5)',
                            shadowBlur: 0,
                            shadowOffsetX: 0,
                            shadowOffsetY: 1,
                        },
                        emphasis: {
                            areaColor: {
                                //image: domImgHover,
                                repeat: 'repeat',
                            },
                            borderColor: '#2ab8ff',
                            borderWidth: 1,
                            shadowColor: 'rgba(0, 255, 255, 0.7)',
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowOffsetY: 1,
                            label: {
                                show: false,
                            },
                        },
                    },
                    zoom: 1,
                    roam: false,
                    //data: data
                },
                {
                    name: '点',
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    symbol: 'pin',
                    symbolSize: function(val) {
                        var a = (maxSize4Pin - minSize4Pin) / (max - min);
                        var b = minSize4Pin - a * min;
                        b = maxSize4Pin - a * max;
                        return a * val[2] + b;
                    },
                    label: {
                        normal: {
                            show: true,
                            textStyle: {
                                color: '#fff',
                                fontSize: 9,
                            },
                            formatter(value) {
                                return value.data.value[2]
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: '#F62157', //标志颜色
                        }
                    },
                    zlevel: 6,
                    data: convertData(data),
                },
                {
                    name: 'Top 5',
                    type: 'effectScatter',
                    coordinateSystem: 'geo',
                    data: convertData(data.sort(function(a, b) {
                        return b.value - a.value;
                    }).slice(0, 5)),
                    symbolSize: function(val) {
                        return val[2] / 10;
                    },
                    showEffectOn: 'render',
                    rippleEffect: {
                        brushType: 'stroke'
                    },
                    hoverAnimation: true,
                    label: {
                        normal: {
                            formatter: '{b}',
                            position: 'right',
                            show: true
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: '#05C3F9',
                            shadowBlur: 10,
                            shadowColor: '#05C3F9'
                        }
                    },
                    zlevel: 1
                },

            ]
        };
        myChart.setOption(option);
    })
})