function extend(o, p) {
    for(prop in p) {                         // For all props in p.
        o[prop] = p[prop];                   // Add the property to o.
    }
    return o;
}
function defineClass(constructor,methods,statics) {
	if (methods) extend(constructor.prototype,methods);
	if (statics) extend(constructor,statics);
	return constructor;
}
var simpleRange=defineClass(function(f,t){this.f=f;this.t=t},
							{includes:function(x){return this.f<=x&&x<=this.t;}},
							{upto:function(){return this.f+"..."+this.t}}
)
console.log(simpleRange);
// 动态原型方式（推荐）
function Car(name, color, price, drivers) {
this.name = name;
this.color = color;
this.price = price;
this.drivers = drivers;
}

Car.prototype.getCarInfo = function () {
console.log(`name: ${this.name},color: ${this.color},price: ${this.price}`);
}

var myCar = new Car('兰博基尼', 'red', '10000000000', ['qaz', 'wsx']);
myCar.drivers.push('mi');
console.log(myCar.drivers); // ["qaz", "wsx", "mi"]

var myCar1 = new Car('兰博基尼1', 'red1', '100000000001', ['qaz1', 'wsx1']);
myCar1.drivers.push('mi1');
console.log(myCar1.drivers); // ["qaz1", "wsx1", "mi1"]
console.log(myCar.drivers);

myCar.getCarInfo();
function  classof(o) {
	return Object.prototype.toString.call(o).slice(8,-1);
}
console.log(classof("45454"));
