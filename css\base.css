@charset "utf-8";
/* CSS Document 
更多精品模板：http://www.bootstrapmb.com
*/

body{ color:#000; background:#FFF; font:12px/1.6 Verdana, Helvetica, sans-serif; text-align:center; }
*{ margin:0; padding:0; }
input,select{ font-size:12px; vertical-align:middle; }
body div{ text-align:left; }
textarea,input{ word-wrap:break-word; word-break:break-all; padding:0px; }
li{ list-style-type:none; }
img{ border:0 none; }
a{ text-decoration:none; color:#333; }
a:link, a:visited{ color:#333; text-decoration: none; outline: none; }
a:hover{ color:#0E4470; text-decoration: none; }
h1{ font-size:24px; }
h2{ font-size:20px; }
h3{ font-size:18px; }
h4{ font-size:16px; }
h5{ font-size:14px; }
h6{ font-size:12px; }
.clearfix:after { visibility: hidden; display: block; font-size: 0; content: " "; clear: both; height: 0; }
* html .clearfix { height: 1%; }
.clearfix { display: block; clear:both; float:none; }
.fleft{ float:left}
.fright{ float:right}
.tleft{ text-align:left}
.tright{ text-align:right}
.tcenter { text-align: center; }
::-moz-selection { background:#70A102; color:#fff; text-shadow: #000 0 1px 0; }
::selection { background: #70A102; color: #fff; text-shadow: #000 0 1px 0; }
.f12 { font-size: 12px; }
.f13 { font-size: 13px; }
.f14 { font-size: 14px; }
.f15 { font-size: 15px; }
.f16 { font-size: 16px; }
.f17 { font-size: 17px; }
.f18 { font-size: 18px; }
.f19 { font-size: 19px; }
.f20 { font-size: 20px; }
.f21 { font-size: 21px; }
.f22 { font-size: 22px; }
.f23 { font-size: 23px; }
.f24 { font-size: 24px; }
.f25 { font-size: 25px; }
.f26 { font-size: 26px; }
.f27 { font-size: 27px; }
.f28 { font-size: 28px; }
.f29 { font-size: 29px; }
.f30 { font-size: 30px; }
.yh { font-family: Microsoft YaHei; }
.noyh{ font-family:Verdana, Helvetica, sans-serif; }
.noBold { font-weight: normal; }
.bold { font-weight: bold; }
.ofHidden { overflow: hidden; }
.pl0 { padding-left: 0; }
.pl1 { padding-left: 1px; }
.pl2 { padding-left: 2px; }
.pl3 { padding-left: 3px; }
.pl4 { padding-left: 4px; }
.pl5 { padding-left: 5px; }
.pl10 { padding-left: 10px; }
.pl15 { padding-left: 15px; }
.pl20 { padding-left: 20px; }
.pl25 { padding-left: 25px; }
.pl30 { padding-left: 30px; }
.pt0 { padding-top: 0; }
.pt1 { padding-top: 1px; }
.pt2 { padding-top: 2px; }
.pt3 { padding-top: 3px; }
.pt4 { padding-top: 4px; }
.pt5 { padding-top: 5px; }
.pt10 { padding-top: 10px; }
.pt15 { padding-top: 15px; }
.pt20 { padding-top: 20px; }
.pt25 { padding-top: 25px; }
.pt30 { padding-top: 30px; }
.pb0 { padding-bottom: 0; }
.pb1 { padding-bottom: 1px; }
.pb2 { padding-bottom: 2px; }
.pb3 { padding-bottom: 3px; }
.pb4 { padding-bottom: 4px; }
.pb5 { padding-bottom: 5px; }
.pb10 { padding-bottom: 10px; }
.pb15 { padding-bottom: 15px; }
.pb20 { padding-bottom: 20px; }
.pb25 { padding-bottom: 25px; }
.pb30 { padding-bottom: 30px; }
.pr0 { padding-right: 0; }
.pr1 { padding-right: 1px; }
.pr2 { padding-right: 2px; }
.pr3 { padding-right: 3px; }
.pr4 { padding-right: 4px; }
.pr5 { padding-right: 5px; }
.pr10 { padding-right: 10px; }
.pr15 { padding-right: 15px; }
.pr20 { padding-right: 20px; }
.pr30 { padding-right: 30px; }
.ml0 { margin-left: 0; }
.ml1 { margin-left: 1px; }
.ml2 { margin-left: 2px; }
.ml3 { margin-left: 3px; }
.ml4 { margin-left: 4px; }
.ml5 { margin-left: 5px; }
.ml10 { margin-left: 10px; }
.ml15 { margin-left: 15px; }
.ml20 { margin-left: 20px; }
.ml25 { margin-left: 25px; }
.ml30 { margin-left: 30px; }
.mr0 { margin-right: 0; }
.mr1 { margin-right: 1px; }
.mr2 { margin-right: 2px; }
.mr3 { margin-right: 3px; }
.mr4 { margin-right: 4px; }
.mr5 { margin-right: 5px; }
.mr10 { margin-right: 10px; }
.mr15 { margin-right: 15px; }
.mr20 { margin-right: 20px; }
.mr25 { margin-right: 25px; }
.mr30 { margin-right: 30px; }
.mt0 { margin-top: 0; }
.mt1 { margin-top: 1px; }
.mt2 { margin-top: 2px; }
.mt3 { margin-top: 3px; }
.mt4 { margin-top: 4px; }
.mt5 { margin-top: 5px; }
.mt10 { margin-top: 10px; }
.mt15 { margin-top: 15px; }
.mt20 { margin-top: 20px; }
.mt25 { margin-top: 25px; }
.mt30 { margin-top: 30px; }
.mb0 { margin-bottom: 0; }
.mb1 { margin-bottom: 1px; }
.mb2 { margin-bottom: 2px; }
.mb3 { margin-bottom: 3px; }
.mb4 { margin-bottom: 4px; }
.mb5 { margin-bottom: 5px; }
.mb10 { margin-bottom: 10px; }
.mb15 { margin-bottom: 15px; }
.mb20 { margin-bottom: 20px; }
.mb25 { margin-bottom: 25px; }
.mb30 { margin-bottom: 30px; }
.pd0{ padding:0; }
.pd1{ padding:1px; }
.pd2{ padding:2px; }
.pd3{ padding:3px; }
.pd4{ padding:4px; }
.pd5{ padding:5px; }
.pd10{ padding:10px; }
.pd15{ padding:15px; }
.pd20{ padding:20px; }
.pd25{ padding:25px; }
.pd30{ padding:30px; }
.border_ccc{ border:1px solid #CCC; }
.box-shadow{ box-shadow: 0 0 15px #cccccc; -webkit-box-shadow: 0 0 15px #cccccc; -moz-box-shadow: 0 0 15px #cccccc; }
.radius4{ -moz-border-radius:4px; -webkit-border-radius:4px; border-radius:4px; }
.radius5{ -moz-border-radius:5px; -webkit-border-radius:5px; border-radius:5px; }
.radius6{ -moz-border-radius:6px; -webkit-border-radius:6px; border-radius:6px; }
.radius7{ -moz-border-radius:7px; -webkit-border-radius:7px; border-radius:7px; }
.radius8{ -moz-border-radius:8px; -webkit-border-radius:8px; border-radius:8px; }
.radius9{ -moz-border-radius:9px; -webkit-border-radius:9px; border-radius:9px; }
.radius10{ -moz-border-radius:10px; -webkit-border-radius:10px; border-radius:10px; }
.block { width:1200px; clear:both; margin:0 auto; }
.text-left{text-align:left}
.text-right{text-align:right}
.text-center{text-align:center}
.text-justify{text-align:justify}
.text-nowrap{white-space:nowrap}
.text-lowercase{text-transform:lowercase}
.text-uppercase{text-transform:uppercase}
.text-capitalize{text-transform:capitalize}
.text-muted{color:#777}

.c_333{ color:#333}
.c_666{ color:#666}
.c_999{ color:#999}

button,
input,
optgroup,
select,
textarea { margin: 0; font: inherit; color: inherit; }
button { overflow: visible; }
button,
select { text-transform: none; }
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] { -webkit-appearance: button; cursor: pointer; }
button[disabled],
html input[disabled] { cursor: default; }
button::-moz-focus-inner,
input::-moz-focus-inner { padding: 0; border: 0; }
input { line-height: normal; }
input[type="checkbox"],
input[type="radio"] { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; padding: 0; }
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button { height: auto; }
input[type="search"] { -webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box; -webkit-appearance: textfield; }
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration { -webkit-appearance: none; }
.btn { display: inline-block; padding: 6px 12px; margin-bottom: 0; font-size: 14px; font-weight: normal; line-height: 1.42857143; text-align: center; white-space: nowrap; vertical-align: middle; -ms-touch-action: manipulation; touch-action: manipulation; cursor: pointer; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; background-image: none; border: 1px solid transparent; border-radius: 4px; }
.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn.active.focus { outline: thin dotted; outline: 5px auto -webkit-focus-ring-color; outline-offset: -2px; }
.btn:hover,
.btn:focus,
.btn.focus { color: #333; text-decoration: none; }
.btn:active,
.btn.active { background-image: none; outline: 0; -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125); box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125); }
.btn-default { color: #333; background-color: #fff; border-color: #ccc; }
.btn-default:focus,
.btn-default.focus { color: #333; background-color: #e6e6e6; border-color: #8c8c8c; }
.btn-default:hover { color: #333; background-color: #e6e6e6; border-color: #adadad; }
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default { color: #333; background-color: #e6e6e6; border-color: #adadad; }
.btn-default:active:hover,
.btn-default.active:hover,
.open > .dropdown-toggle.btn-default:hover,
.btn-default:active:focus,
.btn-default.active:focus,
.open > .dropdown-toggle.btn-default:focus,
.btn-default:active.focus,
.btn-default.active.focus,
.open > .dropdown-toggle.btn-default.focus { color: #333; background-color: #d4d4d4; border-color: #8c8c8c; }
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default { background-image: none; }
.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled.focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default.focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active { background-color: #fff; border-color: #ccc; }
.btn-default .badge { color: #fff; background-color: #333; }
.btn-primary { color: #fff; background-color: #337ab7; border-color: #2e6da4; }
.btn-primary:focus,
.btn-primary.focus { color: #fff; background-color: #286090; border-color: #122b40; }
.btn-primary:hover { color: #fff; background-color: #286090; border-color: #204d74; }
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary { color: #fff; background-color: #286090; border-color: #204d74; }
.btn-primary:active:hover,
.btn-primary.active:hover,
.open > .dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open > .dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open > .dropdown-toggle.btn-primary.focus { color: #fff; background-color: #204d74; border-color: #122b40; }
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary { background-image: none; }
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active { background-color: #337ab7; border-color: #2e6da4; }
.btn-primary .badge { color: #337ab7; background-color: #fff; }
.btn-success { color: #fff; background-color: #5cb85c; border-color: #4cae4c; }
.btn-success:focus,
.btn-success.focus { color: #fff; background-color: #449d44; border-color: #255625; }
.btn-success:hover { color: #fff; background-color: #449d44; border-color: #398439; }
.btn-success:active,
.btn-success.active,
.open > .dropdown-toggle.btn-success { color: #fff; background-color: #449d44; border-color: #398439; }
.btn-success:active:hover,
.btn-success.active:hover,
.open > .dropdown-toggle.btn-success:hover,
.btn-success:active:focus,
.btn-success.active:focus,
.open > .dropdown-toggle.btn-success:focus,
.btn-success:active.focus,
.btn-success.active.focus,
.open > .dropdown-toggle.btn-success.focus { color: #fff; background-color: #398439; border-color: #255625; }
.btn-success:active,
.btn-success.active,
.open > .dropdown-toggle.btn-success { background-image: none; }
.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled.focus,
.btn-success[disabled].focus,
fieldset[disabled] .btn-success.focus,
.btn-success.disabled:active,
.btn-success[disabled]:active,
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success.active { background-color: #5cb85c; border-color: #4cae4c; }
.btn-success .badge { color: #5cb85c; background-color: #fff; }
.btn-info { color: #fff; background-color: #5bc0de; border-color: #46b8da; }
.btn-info:focus,
.btn-info.focus { color: #fff; background-color: #31b0d5; border-color: #1b6d85; }
.btn-info:hover { color: #fff; background-color: #31b0d5; border-color: #269abc; }
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info { color: #fff; background-color: #31b0d5; border-color: #269abc; }
.btn-info:active:hover,
.btn-info.active:hover,
.open > .dropdown-toggle.btn-info:hover,
.btn-info:active:focus,
.btn-info.active:focus,
.open > .dropdown-toggle.btn-info:focus,
.btn-info:active.focus,
.btn-info.active.focus,
.open > .dropdown-toggle.btn-info.focus { color: #fff; background-color: #269abc; border-color: #1b6d85; }
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info { background-image: none; }
.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled.focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info.focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active { background-color: #5bc0de; border-color: #46b8da; }
.btn-info .badge { color: #5bc0de; background-color: #fff; }
.btn-warning { color: #fff; background-color: #f0ad4e; border-color: #eea236; }
.btn-warning:focus,
.btn-warning.focus { color: #fff; background-color: #ec971f; border-color: #985f0d; }
.btn-warning:hover { color: #fff; background-color: #ec971f; border-color: #d58512; }
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning { color: #fff; background-color: #ec971f; border-color: #d58512; }
.btn-warning:active:hover,
.btn-warning.active:hover,
.open > .dropdown-toggle.btn-warning:hover,
.btn-warning:active:focus,
.btn-warning.active:focus,
.open > .dropdown-toggle.btn-warning:focus,
.btn-warning:active.focus,
.btn-warning.active.focus,
.open > .dropdown-toggle.btn-warning.focus { color: #fff; background-color: #d58512; border-color: #985f0d; }
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning { background-image: none; }
.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning.focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active { background-color: #f0ad4e; border-color: #eea236; }
.btn-warning .badge { color: #f0ad4e; background-color: #fff; }
.btn-danger { color: #fff; background-color: #d9534f; border-color: #d43f3a; }
.btn-danger:focus,
.btn-danger.focus { color: #fff; background-color: #c9302c; border-color: #761c19; }
.btn-danger:hover { color: #fff; background-color: #c9302c; border-color: #ac2925; }
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger { color: #fff; background-color: #c9302c; border-color: #ac2925; }
.btn-danger:active:hover,
.btn-danger.active:hover,
.open > .dropdown-toggle.btn-danger:hover,
.btn-danger:active:focus,
.btn-danger.active:focus,
.open > .dropdown-toggle.btn-danger:focus,
.btn-danger:active.focus,
.btn-danger.active.focus,
.open > .dropdown-toggle.btn-danger.focus { color: #fff; background-color: #ac2925; border-color: #761c19; }
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger { background-image: none; }
.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active { background-color: #d9534f; border-color: #d43f3a; }
.btn-danger .badge { color: #d9534f; background-color: #fff; }
.btn-link { font-weight: normal; color: #337ab7; border-radius: 0; }
.btn-link,
.btn-link:active,
.btn-link.active,
.btn-link[disabled],
fieldset[disabled] .btn-link { background-color: transparent; -webkit-box-shadow: none; box-shadow: none; }
.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active { border-color: transparent; }
.btn-link:hover,
.btn-link:focus { color: #23527c; text-decoration: underline; background-color: transparent; }
.btn-link[disabled]:hover,
fieldset[disabled] .btn-link:hover,
.btn-link[disabled]:focus,
fieldset[disabled] .btn-link:focus { color: #777; text-decoration: none; }


@-webkit-keyframes tada{
    0%{-webkit-transform:scale(1);}
    10%, 20%{-webkit-transform:scale(0.9) rotate(-3deg);}
    30%, 50%, 70%, 90%{-webkit-transform:scale(1.1) rotate(3deg);}
    40%, 60%, 80%{-webkit-transform:scale(1.1) rotate(-3deg);}
    100%{-webkit-transform:scale(1) rotate(0);}
}
@-moz-keyframes tada{
    0%{-moz-transform:scale(1);}
    10%, 20%{-moz-transform:scale(0.9) rotate(-3deg);}
    30%, 50%, 70%, 90%{-moz-transform:scale(1.1) rotate(3deg);}
    40%, 60%, 80%{-moz-transform:scale(1.1) rotate(-3deg);}
    100%{-moz-transform:scale(1) rotate(0);}
}
