<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>单子</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <script type="text/javascript" src="js/flexible.debug.js"></script>
    <script type="text/javascript" src="js/flexible_css.debug.js"></script>
    <link rel="stylesheet" type="text/css" href="css/index.css">
</head>

<body>
    <div id="wrap_box">
        <div class="b_con">
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>
    <script type="text/javascript" src="js/jquery-3.2.1.min.js"></script>
    <script>
        function countstart(number) {
            var number = number / 2;
            var count = number;
            var start_str = '';
            if (number % 1 == 0.5) {
                count = number - 0.5;
            }
            //黄色
            for (let index = 0; index < count; index++) {
                start_str += '<img src="/vsteam/share/image/teambox/yellow_start.png" alt="" srcset="">';
            }
            //半色
            var grey_num = 5 - count;
            if (number % 1 == 0.5) {
                start_str += '<img src="/vsteam/share/image/teambox/yellow_start_half.png" alt="" srcset="">';
                grey_num = grey_num - 0.5 - 1;
            }
            //灰色
            for (let i = 0; i < grey_num; i++) {
                start_str += '<img src="/vsteam/share/image/teambox/grey_start.png" alt="" srcset="">';
            }
            return start_str;
        }
        console.log(countstart(8));
    </script>
    <!-- <script type="text/javascript" src="js/try.js"></script> -->
</body>

</html>