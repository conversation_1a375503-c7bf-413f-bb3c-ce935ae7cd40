<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版文字动画函数</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }
        
        .animation_word {
            width: 400px;
            min-height: 300px;
            max-height: 500px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            overflow-y: auto;
            padding: 25px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            transition: all 0.3s ease;
        }
        
        .animation_word:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .animation_word::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            border-radius: 15px 15px 0 0;
        }
        
        .animation_word span {
            color: transparent;
            opacity: 0;
            font-size: 16px;
            line-height: 24px;
            font-weight: 400;
            margin-right: 2px;
            display: inline-block;
            transition: all 0.3s ease;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
        }
        
        .animation_word span.visible {
            color: #ffffff;
            opacity: 1;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn.primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }
        
        .status {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            text-align: center;
            min-height: 20px;
        }
        /* 响应式设计 */
        
        @media (max-width: 768px) {
            .animation_word {
                width: 90vw;
                max-width: 350px;
            }
            .controls {
                flex-direction: column;
                width: 100%;
            }
            .btn {
                width: 100%;
                max-width: 200px;
            }
        }
        /* 滚动条样式 */
        
        .animation_word::-webkit-scrollbar {
            width: 6px;
        }
        
        .animation_word::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }
        
        .animation_word::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }
        
        .animation_word::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 状态显示 -->
        <div class="status" id="status">准备开始...</div>

        <!-- 动画显示区域 -->
        <div class="animation_word" id="animationContainer"></div>

        <!-- 控制按钮 -->
        <div class="controls">
            <button class="btn primary" id="startBtn">开始动画</button>
            <button class="btn secondary" id="pauseBtn">暂停</button>
            <button class="btn" id="resetBtn">重置</button>
            <button class="btn" id="nextBtn">下一段</button>
        </div>
    </div>

    <script>
        /**
         * 简单易用的文字动画函数
         * @param {string|HTMLElement} container - 容器选择器或DOM元素
         * @param {Array} textArray - 文字数组
         * @param {number} fadeSpeed - 淡入速度(毫秒) 默认30
         * @param {number} paragraphDelay - 段落间隔时间(秒) 默认3
         * @param {Object} options - 其他可选配置
         * @returns {Object} 返回控制对象，包含start, stop, pause, resume等方法
         */
        function createTextAnimation(container, textArray, fadeSpeed = 30, paragraphDelay = 3, options = {}) {
            // 获取容器元素
            const containerElement = typeof container === 'string' ?
                document.querySelector(container) :
                container;

            if (!containerElement) {
                throw new Error('容器元素未找到');
            }

            if (!Array.isArray(textArray) || textArray.length === 0) {
                throw new Error('文字数组不能为空');
            }

            // 配置参数
            const config = {
                charSpeed: options.charSpeed || 80, // 字符显示间隔
                fadeSpeed: fadeSpeed, // 淡入速度
                paragraphDelay: paragraphDelay * 1000, // 段落间隔(转换为毫秒)
                fadeSteps: options.fadeSteps || 10, // 淡入步数
                autoLoop: options.autoLoop !== false, // 是否自动循环
                autoStart: options.autoStart !== false // 是否自动开始
            };

            // 状态变量
            let currentIndex = 0;
            let isPlaying = false;
            let isPaused = false;
            let currentAnimation = null;
            let currentCharIndex = 0;
            let currentText = '';

            // 创建字符元素
            function createCharSpan(char) {
                const span = document.createElement('span');
                span.textContent = char;
                span.style.cssText = `
                    opacity: 0;
                    color: transparent;
                    display: inline-block;
                    transition: all 0.3s ease;
                    margin-right: 1px;
                `;
                return span;
            }

            // 字符淡入动画
            function animateCharFadeIn(span) {
                let opacity = 0;
                const fadeInterval = setInterval(() => {
                    opacity++;
                    span.style.opacity = opacity / config.fadeSteps;

                    if (opacity >= config.fadeSteps) {
                        clearInterval(fadeInterval);
                        span.style.color = '#ffffff';
                        span.style.opacity = '1';
                        span.style.textShadow = '0 0 10px rgba(255, 255, 255, 0.5)';
                    }
                }, config.fadeSpeed);
            }

            // 动画下一个字符
            function animateNextChar() {
                if (isPaused || !isPlaying) return;

                if (currentCharIndex >= currentText.length) {
                    onTextComplete();
                    return;
                }

                const char = currentText[currentCharIndex];
                const span = createCharSpan(char);
                containerElement.appendChild(span);

                animateCharFadeIn(span);
                currentCharIndex++;

                currentAnimation = setTimeout(() => {
                    animateNextChar();
                }, config.charSpeed);
            }

            // 动画文本
            function animateText(text) {
                currentText = text;
                currentCharIndex = 0;
                animateNextChar();
            }

            // 文本完成回调
            function onTextComplete() {
                if (config.autoLoop && textArray.length > 1) {
                    currentAnimation = setTimeout(() => {
                        currentIndex = (currentIndex + 1) % textArray.length;
                        containerElement.innerHTML = '';
                        animateText(textArray[currentIndex]);
                    }, config.paragraphDelay);
                } else {
                    isPlaying = false;
                }
            }

            // 停止动画
            function stop() {
                if (currentAnimation) {
                    clearTimeout(currentAnimation);
                    currentAnimation = null;
                }
                isPlaying = false;
                isPaused = false;
            }

            // 公共API
            const api = {
                // 开始动画
                start() {
                    if (isPlaying && !isPaused) return api;

                    if (isPaused) {
                        isPaused = false;
                        if (currentText && currentCharIndex < currentText.length) {
                            currentAnimation = setTimeout(() => {
                                animateNextChar();
                            }, config.charSpeed);
                        }
                        return api;
                    }

                    isPlaying = true;
                    currentIndex = 0;
                    containerElement.innerHTML = '';
                    animateText(textArray[currentIndex]);
                    return api;
                },

                // 暂停动画
                pause() {
                    if (!isPlaying) return api;
                    isPaused = true;
                    if (currentAnimation) {
                        clearTimeout(currentAnimation);
                        currentAnimation = null;
                    }
                    return api;
                },

                // 恢复动画
                resume() {
                    if (!isPaused) return api;
                    isPaused = false;
                    if (currentText && currentCharIndex < currentText.length) {
                        currentAnimation = setTimeout(() => {
                            animateNextChar();
                        }, config.charSpeed);
                    }
                    return api;
                },

                // 停止动画
                stop() {
                    stop();
                    containerElement.innerHTML = '';
                    currentIndex = 0;
                    currentCharIndex = 0;
                    currentText = '';
                    return api;
                },

                // 下一段
                next() {
                    if (!isPlaying) return api;
                    stop();
                    currentIndex = (currentIndex + 1) % textArray.length;
                    containerElement.innerHTML = '';
                    isPlaying = true;
                    animateText(textArray[currentIndex]);
                    return api;
                },

                // 设置新的文字数组
                setTextArray(newTextArray) {
                    if (!Array.isArray(newTextArray) || newTextArray.length === 0) {
                        throw new Error('文字数组不能为空');
                    }
                    textArray = newTextArray;
                    this.stop();
                    return api;
                },

                // 更新配置
                updateConfig(newConfig) {
                    Object.assign(config, newConfig);
                    if (newConfig.paragraphDelay) {
                        config.paragraphDelay = newConfig.paragraphDelay * 1000;
                    }
                    return api;
                },

                // 获取状态
                getStatus() {
                    return {
                        isPlaying,
                        isPaused,
                        currentIndex,
                        currentCharIndex,
                        totalTexts: textArray.length,
                        currentText
                    };
                },

                // 获取配置
                getConfig() {
                    return {...config
                    };
                }
            };

            // 自动开始
            if (config.autoStart) {
                setTimeout(() => {
                    api.start();
                }, 100);
            }

            return api;
        }

        // 使用示例
        const textArray = [
            "欢迎来到简化版文字动画效果！",
            "这个函数只需要传入文字数组、淡入速度和段落间隔时间。",
            "使用起来非常简单，一行代码就能创建动画效果。",
            "感谢您的使用！"
        ];

        // 创建动画实例 - 超简单调用方式
        const animation = createTextAnimation(
            '#animationContainer', // 容器
            textArray, // 文字数组
            30, // 淡入速度(毫秒)
            3 // 段落间隔(秒)
        );

        // 绑定控制按钮
        document.getElementById('startBtn').addEventListener('click', () => animation.start());
        document.getElementById('pauseBtn').addEventListener('click', () => {
            const status = animation.getStatus();
            if (status.isPaused) {
                animation.resume();
                document.getElementById('pauseBtn').textContent = '暂停';
            } else {
                animation.pause();
                document.getElementById('pauseBtn').textContent = '继续';
            }
        });
        document.getElementById('resetBtn').addEventListener('click', () => {
            animation.stop();
            document.getElementById('pauseBtn').textContent = '暂停';
        });
        document.getElementById('nextBtn').addEventListener('click', () => animation.next());

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                const status = animation.getStatus();
                if (status.isPlaying && !status.isPaused) {
                    animation.pause();
                }
            }
        });
    </script>
</body>

</html>