/**
 * 简单易用的文字动画函数
 * 
 * 使用方法：
 * const animation = createTextAnimation(container, textArray, fadeSpeed, paragraphDelay);
 * 
 * @param {string|HTMLElement} container - 容器选择器或DOM元素
 * @param {Array} textArray - 文字数组
 * @param {number} fadeSpeed - 淡入速度(毫秒) 默认30
 * @param {number} paragraphDelay - 段落间隔时间(秒) 默认3
 * @param {Object} options - 其他可选配置
 * @returns {Object} 返回控制对象，包含start, stop, pause, resume等方法
 * 
 * 示例：
 * const textArray = ["第一段文字", "第二段文字", "第三段文字"];
 * const animation = createTextAnimation('#container', textArray, 30, 3);
 * animation.start(); // 开始动画
 */
function createTextAnimation(container, textArray, fadeSpeed = 30, paragraphDelay = 3, options = {}) {
    // 获取容器元素
    const containerElement = typeof container === 'string' 
        ? document.querySelector(container) 
        : container;
    
    if (!containerElement) {
        throw new Error('容器元素未找到');
    }
    
    if (!Array.isArray(textArray) || textArray.length === 0) {
        throw new Error('文字数组不能为空');
    }
    
    // 配置参数
    const config = {
        charSpeed: options.charSpeed || 80,        // 字符显示间隔(毫秒)
        fadeSpeed: fadeSpeed,                      // 淡入速度(毫秒)
        paragraphDelay: paragraphDelay * 1000,    // 段落间隔(转换为毫秒)
        fadeSteps: options.fadeSteps || 10,       // 淡入步数
        autoLoop: options.autoLoop !== false,     // 是否自动循环
        autoStart: options.autoStart !== false    // 是否自动开始
    };
    
    // 状态变量
    let currentIndex = 0;
    let isPlaying = false;
    let isPaused = false;
    let currentAnimation = null;
    let currentCharIndex = 0;
    let currentText = '';
    
    // 创建字符元素
    function createCharSpan(char) {
        const span = document.createElement('span');
        span.textContent = char;
        span.style.cssText = `
            opacity: 0;
            color: transparent;
            display: inline-block;
            transition: all 0.3s ease;
            margin-right: 2px;
            font-size: 16px;
            line-height: 24px;
        `;
        return span;
    }
    
    // 字符淡入动画
    function animateCharFadeIn(span) {
        let opacity = 0;
        const fadeInterval = setInterval(() => {
            opacity++;
            span.style.opacity = opacity / config.fadeSteps;
            
            if (opacity >= config.fadeSteps) {
                clearInterval(fadeInterval);
                span.style.color = '#ffffff';
                span.style.opacity = '1';
                span.style.textShadow = '0 0 10px rgba(255, 255, 255, 0.5)';
            }
        }, config.fadeSpeed);
    }
    
    // 动画下一个字符
    function animateNextChar() {
        if (isPaused || !isPlaying) return;
        
        if (currentCharIndex >= currentText.length) {
            onTextComplete();
            return;
        }
        
        const char = currentText[currentCharIndex];
        const span = createCharSpan(char);
        containerElement.appendChild(span);
        
        animateCharFadeIn(span);
        currentCharIndex++;
        
        currentAnimation = setTimeout(() => {
            animateNextChar();
        }, config.charSpeed);
    }
    
    // 动画文本
    function animateText(text) {
        currentText = text;
        currentCharIndex = 0;
        animateNextChar();
    }
    
    // 文本完成回调
    function onTextComplete() {
        if (config.autoLoop && textArray.length > 1) {
            currentAnimation = setTimeout(() => {
                currentIndex = (currentIndex + 1) % textArray.length;
                containerElement.innerHTML = '';
                animateText(textArray[currentIndex]);
            }, config.paragraphDelay);
        } else {
            isPlaying = false;
        }
    }
    
    // 停止动画
    function stop() {
        if (currentAnimation) {
            clearTimeout(currentAnimation);
            currentAnimation = null;
        }
        isPlaying = false;
        isPaused = false;
    }
    
    // 公共API
    const api = {
        // 开始动画
        start() {
            if (isPlaying && !isPaused) return api;
            
            if (isPaused) {
                isPaused = false;
                if (currentText && currentCharIndex < currentText.length) {
                    currentAnimation = setTimeout(() => {
                        animateNextChar();
                    }, config.charSpeed);
                }
                return api;
            }
            
            isPlaying = true;
            currentIndex = 0;
            containerElement.innerHTML = '';
            animateText(textArray[currentIndex]);
            return api;
        },
        
        // 暂停动画
        pause() {
            if (!isPlaying) return api;
            isPaused = true;
            if (currentAnimation) {
                clearTimeout(currentAnimation);
                currentAnimation = null;
            }
            return api;
        },
        
        // 恢复动画
        resume() {
            if (!isPaused) return api;
            isPaused = false;
            if (currentText && currentCharIndex < currentText.length) {
                currentAnimation = setTimeout(() => {
                    animateNextChar();
                }, config.charSpeed);
            }
            return api;
        },
        
        // 停止动画
        stop() {
            stop();
            containerElement.innerHTML = '';
            currentIndex = 0;
            currentCharIndex = 0;
            currentText = '';
            return api;
        },
        
        // 下一段
        next() {
            if (!isPlaying) return api;
            stop();
            currentIndex = (currentIndex + 1) % textArray.length;
            containerElement.innerHTML = '';
            isPlaying = true;
            animateText(textArray[currentIndex]);
            return api;
        },
        
        // 设置新的文字数组
        setTextArray(newTextArray) {
            if (!Array.isArray(newTextArray) || newTextArray.length === 0) {
                throw new Error('文字数组不能为空');
            }
            textArray = newTextArray;
            this.stop();
            return api;
        },
        
        // 更新配置
        updateConfig(newConfig) {
            Object.assign(config, newConfig);
            if (newConfig.paragraphDelay) {
                config.paragraphDelay = newConfig.paragraphDelay * 1000;
            }
            return api;
        },
        
        // 获取状态
        getStatus() {
            return {
                isPlaying,
                isPaused,
                currentIndex,
                currentCharIndex,
                totalTexts: textArray.length,
                currentText
            };
        },
        
        // 获取配置
        getConfig() {
            return { ...config };
        }
    };
    
    // 自动开始
    if (config.autoStart) {
        setTimeout(() => {
            api.start();
        }, 100);
    }
    
    return api;
}

// 如果在Node.js环境中，导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = createTextAnimation;
}

// 如果支持ES6模块，也提供默认导出
if (typeof window !== 'undefined') {
    window.createTextAnimation = createTextAnimation;
}
