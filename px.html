<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .px {
            background-color: greenyellow;
            overflow: hidden;
        }
        
        .px>div {
            height: 100px;
            width: 100px;
            border: 1px solid;
            float: left;
        }
    </style>
</head>

<body>
    <div class="px">
        <div></div>
        <div></div>
        <div></div>
    </div>
    <script>
        var height = 1.88;

        console.log(outer)

        function outer() {
            var age = 18;

            function inner() {
                var name = 'why';
                console.log(name, age, height);
            }
            return inner;
        }
        var fn = outer();
        fn();
    </script>
</body>

</html>