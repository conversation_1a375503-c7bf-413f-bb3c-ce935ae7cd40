<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渐变进度条</title>
    <style>
        .progress-bar {
            width: calc(120px + 9px);
            display: flex;
            justify-content: space-between;
        }
        
        .progress-bar>span {
            height: 4px;
            width: 12px;
            background-color: black;
            margin-right: 1px;
            /* background: linear-gradient(90deg, #fff, #000); */
        }
    </style>
</head>

<body>

    <div class="progress-bar">
        <!-- <span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span> -->
    </div>

    <script>
        function splitGradient(gradient) {
            const start = parseInt(hexToDec(gradient[0]), 16);
            const end = parseInt(hexToDec(gradient[1]), 16);

            const steps = 10; // 分成十份
            const increment = (end - start) / steps;
            console.log(start)

            let currentColor = start;
            let splitGradient = [];

            for (let i = 0; i < steps; i++) {
                splitGradient.push(decimalToHexColor(currentColor));
                currentColor += increment;
            }

            splitGradient.push(decimalToHexColor(end)); // 确保最后一个颜色是结束颜色
            return splitGradient;
        }

        function decimalToHexColor(decimal) {
            var hex = decimal.toString(16);
            // 如果十六进制颜色代码不足6位，补全
            while (hex.length < 6) {
                hex = '0' + hex;
            }
            // 确保结果是以'#'开头
            return '#' + hex;
        }

        function hexToDec(hexColor) {
            // 移除十六进制颜色中的'#'（如果有）
            const hex = hexColor.replace(/^\s*#|\s*$/g, '');

            // 对于三位短十六进制颜色，重复每位两次
            if (hex.length === 3) {
                hex = hex.replace(/(.)/g, '$1$1');
            }

            // 使用parseInt转换为十进制
            return parseInt(hex, 16);
        }
        // 示例使用：
        const gradient = ['#ffffff', '#000000']; // 从黑到红的渐变
        const splitGradientColors = splitGradient(gradient);
        console.log(splitGradientColors);
        var htmls = '';
        for (var index = 0; index < 10; index++) {
            htmls += `<span style='background: linear-gradient(90deg, ${splitGradientColors[index]}, ${splitGradientColors[index+1]})'></span>`
        }
        var gress = document.querySelector('.progress-bar').innerHTML = htmls;
        //gress.innerHTML = htmls
    </script>

</body>

</html>