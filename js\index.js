var n=3;
switch(n){
	case 1:
		console.log(1)
	break;
	case 2:
		console.log(2)
	break;
	default:
		console.log('无法匹配');
	break;
}
function convert(x) {
	switch(typeof x){
		case 'number':
			return x.toString(16);
		break;
		case 'string':
			return '"'+x+'"';
		break;
		default:
			return String(x);
		break;
	}
}
console.log(convert(false));
var arr={
	age:13,
	number:2,
	name:"xiaoming"
}
var aii={
	1:22,
	3:44,
	name:2,
	2:544
}
for(k in aii){
	console.log(k+":"+aii[k]);
}
var a=[],i=0;
for(a[i++] in arr);
	console.log(a);
function factorial(x){
	if (x<0) throw new Error('x不能是负数');
	for (var f = 1; x>1;f*=x,x--) ;
	return f;
}
factorial(6);
try{
	//var n=Number(prompt('请输入一个正整数'),"");
	var f=factorial(n);
	//alert(n+"!="+f);
}
catch(ex){
	alert(ex);
}
var bdd={
	a:33,
	name:'sx'
}
console.log(bdd);
function inherit(p){
	if(p==null) throw TypeError();
	if (Object.create) {
		return Object.create(p);
	}
	var t=typeof p;
	if (t!=="objiect"&& t!=="function") {
		throw TypeError();
	}
	function f(){};
	f.prototype=p;
	return new f();
}
var bss=inherit(bdd);
console.log(bss);
var port={
	a:100,
	b:10
}
var price={
	a:20,
	b:12
}
function pdd(ports){
	var total=0.0;
	for(items in ports){
		var db=ports[items];
		var pr=price[items];
		total+=db*pr; 
	}
	return total;
}
console.log(pdd(port));
var o={
	x:1,
	sub:12
}
var pi=inherit(o);
pi.y=2;
var q=inherit(pi);
//q.x=3;
//o.x=4;
var asd=o.x;
console.log(o);
//delete o.x;
console.log(o);
console.log(asd);
//console.log(q);
for(it in q){
	//console.log(q[it]);
}
// var iid=o&&o.sub&&o.sub.length;
// console.log(iid);
console.log(o.hasOwnProperty('x'));
console.log(Object.keys(pi));
console.log(Object.getOwnPropertyNames(pi));
var p={
	x:1.0,
	y:1.0,
	get r(){return Math.sqrt(this.x*this.x+this.y*this.y)},
	set r(newvalue){
		var oldvalue=Math.sqrt(this.x*this.x+this.y*this.y);
		var ratio=newvalue/oldvalue;
		this.x*=ratio;
		this.y*=ratio;
	},
	get theta(){return Math.atan2(this.y,this.x);
		//return this.x+this.y;
	}

}

var ns=inherit(p);
ns.x=1,ns.y=1;
console.log(ns.x);
console.log(ns.r);
console.log(ns.theta);
console.log(Object.keys(ns));
console.log(Object.getOwnPropertyDescriptor(pi,'x'));
console.log(Object.getOwnPropertyDescriptor(o,'x'));
Object.defineProperty(o,'x',{
	value:2,
	writable:true,
	enumerable:false,
	configurable:true
})
console.log(o.x);
console.log(o);
console.log(Object.keys(o));
for(ites in o){
	console.log(o[ites]);
}
Object.defineProperty(o,'x',{
	//writable:false
})
o.x=3;
console.log(o.x);
Object.defineProperty(o,'x',{
	value:3
})
console.log(o.x);
Object.defineProperty(o,'x',{
	get:function(){
		return 56;
	},
	set:function(n){
		if (n>5) {
			console.log('yi');
		}else{
			console.log('小于一');
		}
		
	}
	
});
console.log(o.x);
o.x=3;
console.log(o.x);
console.log(o);
function classof(o) {
	if (o===null) {
		return "null";
	}
	if (o===undefined) {
		return 'Undefined';
	}
	return Object.prototype.toString.call(o).slice(8,-1);
}
console.log(classof(o));
console.log(Object.isExtensible(o));
//Object.seal(o);
//Object.preventExtensions(o);
console.log(Object.isExtensible(o));
o.t=3;
console.log(Object.isSealed(o));
var bds={
	a:12
}

var bii=inherit(bds);
bii.y=3;
console.log(bii);
console.log(JSON.stringify(bii));
var book={
	_year:2004,
	edition:1
}
Object.defineProperty(book,"year",{
	get:function(){
		return this._year;
	},
	set:function(vals){

	}
})
console.log(book.year);
var  dongd={};
if (Object.keys(dongd).length===0) {
	console.log(12);
}

var cptime=timeConvert(1596439800,0);
var time = cptime.split(' ')[0];
console.log(time);
/*时间戳转换为时间*/
function getDate(date){ 
    
}
function timeConvert(timestamp,num){//num:0 YYYY-MM-DD  num:1  YYYY-MM-DD hh:mm:ss // timestamp:时间戳 
            timestamp = timestamp+'';
            timestamp = timestamp.length==10?timestamp*1000:timestamp;
            var date = new Date(timestamp);
            var y = date.getFullYear();  
            var m = date.getMonth() + 1;  
            m = m < 10 ? ('0' + m) : m;  
            var d = date.getDate();  
            d = d < 10 ? ('0' + d) : d;  
            var h = date.getHours();
            h = h < 10 ? ('0' + h) : h;
            var minute = date.getMinutes();
            var second = date.getSeconds();
            minute = minute < 10 ? ('0' + minute) : minute;  
            second = second < 10 ? ('0' + second) : second; 
            if(num==0){
                return y + '/' + m + '/' + d;  
            }else{
                return y + '-' + m + '-' + d +' '+ h +':'+ minute +':' + second;  
            }
}