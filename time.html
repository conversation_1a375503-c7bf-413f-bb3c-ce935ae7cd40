<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <script src="./js/moment.min.js"></script>

    <script>
        function weekdays(week) {

            var str = '';
            switch (week) {
                case '5':
                    str = '周五'
            }
            return str;
        }
        // moment().format();
        //moment.locale('zh-cn');
        var day = moment('').add(1, 'days').format('MM-DD');
        var day = weekdays(moment('2024-01-12').format('d'));

        console.log(day);

        var obj = [{
            'da': '123'
        }]
        var str = 'da';
        console.log(obj[0][str]);
        //var url = '?str';
        var date = 6;

        var weekday = moment('2024').week(date + 3).add(1, 'days').format('MM/DD');
        var lastweekday = moment('2024').week(date + 3).add(7, 'days').format('MM/DD');
        console.log(weekday, lastweekday);
        var date = '2023-01-22';
        console.log(date.split('-')[0]);
        // var arr = [];
        // arr.length = 10;
        // arr.fill(0);
        var ar = Array(10).fill(0);
        console.log(ar);
    </script>
</body>

</html>