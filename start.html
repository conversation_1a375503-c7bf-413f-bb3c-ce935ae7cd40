<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <div class="star_contrast_mod">
        <div>
            <span class="close_star" data-lang="string_lang_close">关闭</span>
            <div class="star_content">
                <div class="star_title" data-lang="string_lang_teamcomparsion">球队对比</div>
                <div class="items_names"><span>跑动</span>(<span>km/h</span>)</div>
                <div class="star_lines"></div>
                <div class="star_canvas">

                </div>
            </div>
        </div>
    </div>
    <script>
        //团队篮球球星对比
        var upload = data.uploaded * 1;
        $('.close_star').on('click', function() {
            $('.star_contrast_mod').toggle();
        })
        var star_chart = echarts.init(document.getElementsByClassName('star_canvas')[0]);
        $('.run_contrast>div').on('click', function() {
            $('.items_names>span:eq(0)').html('场均跑动');
            $('.items_names>span:eq(1)').html('千米/场');
            star_value = [Math.round(data.sumRun / 100) / 10, ((8.1 * upload).toFixed(1)) * 1, ((9.2 * upload).toFixed(1)) * 1, ((10.7 * upload).toFixed(1)) * 1];
            star_bar(star_chart, star_value);
            $('.star_contrast_mod').toggle();
        })
        $('.data_contrast:eq(0),.data_contrast:eq(1)').on('click', function() {
                var star_value = [data.jumpAvgHeight, 42.5, 45.3, 96.52];
                $('.items_names>span:eq(0)').html('纵跳高度');
                $('.items_names>span:eq(1)').html('cm');
                star_bar(star_chart, star_value);
                $('.star_contrast_mod').toggle();
            })
            // $('.data_contrast:eq(2)').on('click', function() {
            //     var star_value = [data.maxTakeOffDistance, 165, 171, 305];
            //     $('.items_names>span:eq(0)').html('纵跳距离');
            //     $('.items_names>span:eq(1)').html('cm');
            //     star_bar(star_chart, star_value);
            //     $('.star_contrast_mod').toggle();
            // })
        $('.data_contrast:eq(2),.data_contrast:eq(3)').on('click', function() {
                var star_value = [data.avgDuration, 665, 688, 920];
                $('.items_names>span:eq(0)').html('腾空时间');
                $('.items_names>span:eq(1)').html('ms');
                star_bar(star_chart, star_value);
                $('.star_contrast_mod').toggle();
            })
            // 球星对比，团队足球模块
        var upload = data.uploaded * 1;
        $('.close_star').on('click', function() {
            $('.star_contrast_mod').toggle();
        })
        var star_chart = echarts.init(document.getElementsByClassName('star_canvas')[0]);
        $('.run_contrast>div').on('click', function() {
            $('.items_names>span:eq(0)').html('场均跑动');
            $('.items_names>span:eq(1)').html('千米/场');
            star_value = [Math.round(data.sumRun / 100) / 10, ((8.1 * upload).toFixed(1)) * 1, ((9.2 * upload).toFixed(1)) * 1, ((10.7 * upload).toFixed(1)) * 1];
            star_bar(star_chart, star_value);
            $('.star_contrast_mod').toggle();
        })
        $('.data_contrast:eq(0),.data_contrast:eq(1)').on('click', function() {
                var star_value = [data.jumpAvgHeight, 42.5, 45.3, 96.52];
                $('.items_names>span:eq(0)').html('纵跳高度');
                $('.items_names>span:eq(1)').html('cm');
                star_bar(star_chart, star_value);
                $('.star_contrast_mod').toggle();
            })
            // $('.data_contrast:eq(2)').on('click', function() {
            //     var star_value = [data.maxTakeOffDistance, 165, 171, 305];
            //     $('.items_names>span:eq(0)').html('纵跳距离');
            //     $('.items_names>span:eq(1)').html('cm');
            //     star_bar(star_chart, star_value);
            //     $('.star_contrast_mod').toggle();
            // })
        $('.data_contrast:eq(2),.data_contrast:eq(3)').on('click', function() {
                var star_value = [data.avgDuration, 665, 688, 920];
                $('.items_names>span:eq(0)').html('腾空时间');
                $('.items_names>span:eq(1)').html('ms');
                star_bar(star_chart, star_value);
                $('.star_contrast_mod').toggle();
            })
            // 球星对比模块，个人篮球
        var star_chart = echarts.init(document.getElementsByClassName('star_canvas')[0]);
        $('.close_star').on('click', function() {
            $('.contrast_hm').css('display', 'none');
            $('.star_contrast_mod').toggle();
        })
        $('.run_contrast>div').on('click', function() {
            $('.items_names>span:eq(0)').html(languages ? 'avg rundistance' : '场均跑动');
            $('.items_names>span:eq(1)').html('km');
            var star_value = [Math.round(data.runDistance / 100) / 10, 8.1, 9.2, 10.7];
            star_bar(star_chart, star_value);
            $('.star_contrast_mod').toggle();
        })
        $('.data_contrast:eq(0),.data_contrast:eq(1)').on('click', function() {
            var star_value = [data.maxTakeOffHeight, 42.5, 45.3, 96.52];
            $('.items_names>span:eq(0)').html(languages ? 'Average height' : '纵跳高度');
            $('.items_names>span:eq(1)').html('cm');
            star_bar(star_chart, star_value);
            $('.star_contrast_mod').toggle();
        })
        $('.data_contrast:eq(2),.data_contrast:eq(3)').on('click', function() {
            var star_value = [data.maxDuration, 665, 688, 920];
            $('.items_names>span:eq(0)').html(languages ? 'Take off Time' : '滞空时间');
            $('.items_names>span:eq(1)').html('ms');
            star_bar(star_chart, star_value);
            $('.star_contrast_mod').toggle();
        })
        $('.most_contrast').on('click', function() {
                var star_value = [data.maxSprintSpeed, 28.5, 30.1, 31.84];
                $('.items_names>span:eq(0)').html(languages ? 'Distance' : '跑动');
                $('.items_names>span:eq(1)').html('km/h');
                $('.contrast_hm').css('display', 'block');
                if (data.maxSprintSpeed) {
                    var hm_data = (100 / (data.maxSprintSpeed * 1000 / 360)).toFixed(2);
                } else {
                    var hm_data = 0;
                }
                $('.mt_hm_data').html(hm_data);
                star_bar(star_chart, star_value);
                $('.star_contrast_mod').toggle();
            })
            // 球星对比模块，个人足球
        var star_chart = echarts.init(document.getElementsByClassName('star_canvas')[0]);
        $('.close_star').on('click', function() {
            $('.contrast_hm').css('display', 'none');
            $('.star_contrast_mod').toggle();
        })
        $('.run_contrast>div').on('click', function() {
            $('.items_names>span:eq(0)').html(languages ? 'avg rundistance' : '场均跑动');
            $('.items_names>span:eq(1)').html('km');
            var star_value = [Math.round(data.runDistance / 100) / 10, 8.1, 9.2, 10.7];
            star_bar(star_chart, star_value);
            $('.star_contrast_mod').toggle();
        })
        $('.data_contrast:eq(0),.data_contrast:eq(1)').on('click', function() {
                var star_value = [data.maxTakeOffHeight, 42.5, 45.3, 96.52];
                $('.items_names>span:eq(0)').html(languages ? 'Average height' : '纵跳高度');
                $('.items_names>span:eq(1)').html('cm');
                star_bar(star_chart, star_value);
                $('.star_contrast_mod').toggle();
            })
            // $('.data_contrast:eq(2)').on('click', function() {
            //     var star_value = [data.maxTakeOffDistance, 165, 171, 305];
            //     $('.items_names>span:eq(0)').html('纵跳距离');
            //     $('.items_names>span:eq(1)').html('cm');
            //     star_bar(star_chart, star_value);
            //     $('.star_contrast_mod').toggle();
            // })
        $('.data_contrast:eq(2),.data_contrast:eq(3)').on('click', function() {
            var star_value = [data.maxDuration, 665, 688, 920];
            $('.items_names>span:eq(0)').html(languages ? 'Take off Time' : '滞空时间');
            $('.items_names>span:eq(1)').html('ms');
            star_bar(star_chart, star_value);
            $('.star_contrast_mod').toggle();
        })
        $('.most_contrast').on('click', function() {
            var star_value = [data.maxSprintSpeed, 28.5, 30.1, 31.84];
            $('.items_names>span:eq(0)').html(languages ? 'Distance' : '跑动');
            $('.items_names>span:eq(1)').html('km/h');
            $('.contrast_hm').css('display', 'block');
            if (data.maxSprintSpeed) {
                var hm_data = (100 / (data.maxSprintSpeed * 1000 / 360)).toFixed(2);
            } else {
                var hm_data = 0;
            }
            $('.mt_hm_data').html(hm_data);
            star_bar(star_chart, star_value);
            $('.star_contrast_mod').toggle();
        })

        //足球
        function star_bar(myChart, star_value) {
            // console.log(run_x);
            let category = ['本队', '同龄', '中超', '欧洲联赛'];
            let barData = [{
                    value: star_value[0],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: ' #2484FF'
                        }, {
                            offset: 1,
                            color: '#4ABAFF'
                        }])
                    }
                }, {
                    value: star_value[1],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#38DD5F'
                        }, {
                            offset: 1,
                            color: '#7EF4A3'
                        }])
                    }
                }, {
                    value: star_value[2],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#9474FF'
                        }, {
                            offset: 1,
                            color: '#C6B5FF'
                        }])
                    }
                }, {
                    value: star_value[3],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#FF8049'
                        }, {
                            offset: 1,
                            color: '#FFBDA0'
                        }])
                    }
                }]
                //跑动曲线
            var option = {
                backgroundColor: '#fff',
                grid: {
                    top: '0%',
                    left: '0%',
                    right: '0%',
                    bottom: '0%',
                    containLabel: true
                },
                xAxis: [{
                    data: category, //x轴名称
                    axisLabel: {
                        textStyle: {
                            color: '#06162A',
                            fontSize: 12
                        },
                        interval: 0
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ccc'
                        }

                    }
                }, {
                    name: '',
                    data: star_value, //顶上x轴数据名称
                    axisLabel: {
                        margin: 7,
                        textStyle: {
                            color: '#2484FF',
                            fontSize: 12
                        },
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ccc',
                            width: 0.5
                        }
                    },
                    nameTextStyle: {
                        color: '#333',
                        fontSize: 14,
                        fontWeight: 400,
                        padding: [10, -30, 0, 0],
                    },
                    splitLine: {
                        show: false,
                        lineStyle: {
                            color: '#E4E4E4',
                            type: 'dashed',
                        },
                    },
                }],
                yAxis: {
                    splitLine: {
                        show: true
                    },
                    splitArea: {
                        show: false
                    },
                    splitNumber: 3,
                    axisLabel: {
                        show: false,
                        textStyle: {
                            color: '#000'
                        },
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#E1EEFF',

                        }
                    }
                },
                series: [{
                    symbolSize: 4,
                    itemStyle: {
                        color: 'rgba(0,0,0,0)',
                        borderColor: '#fff'
                    },
                    data: [
                        [0, star_value[3] / 15],
                        [1, star_value[3] / 15],
                        [2, star_value[3] / 15],
                        [3, star_value[3] / 15]
                    ],
                    type: 'scatter'
                }, {
                    name: 'bar',
                    type: 'bar',
                    showBackground: true,
                    markLine: { //x轴变成虚线(标线)
                        symbol: 'none',
                        label: {
                            show: true,
                            position: 'end',
                            formatter: '',
                            color: '#EA7683',
                        },
                        lineStyle: {
                            type: 'dashed',
                            color: '#2484FF;',
                            width: 1,
                        },
                        data: [{
                            name: '刻度标线',
                            yAxis: 0,
                        }, ],
                    },
                    backgroundStyle: {
                        borderRadius: 6
                    },
                    barWidth: 12,
                    itemStyle: {
                        borderRadius: 6
                    },
                    data: barData
                }]
            };
            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        }
        //篮球
        function star_bar(myChart, star_value) {
            // console.log(run_x);
            let category = ['我的', '同龄', '村BA', 'CBA/NBA'];
            let barData = [{
                    value: star_value[0],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: ' #2484FF'
                        }, {
                            offset: 1,
                            color: '#4ABAFF'
                        }])
                    }
                }, {
                    value: star_value[1],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#38DD5F'
                        }, {
                            offset: 1,
                            color: '#7EF4A3'
                        }])
                    }
                }, {
                    value: star_value[2],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#9474FF'
                        }, {
                            offset: 1,
                            color: '#C6B5FF'
                        }])
                    }
                }, {
                    value: star_value[3],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#FF8049'
                        }, {
                            offset: 1,
                            color: '#FFBDA0'
                        }])
                    }
                }]
                //跑动曲线
            option = {
                backgroundColor: '#fff',
                grid: {
                    top: '0%',
                    left: '0%',
                    right: '0%',
                    bottom: '0%',
                    containLabel: true
                },
                xAxis: [{
                    data: category, //x轴名称
                    axisLabel: {
                        textStyle: {
                            color: '#06162A',
                            fontSize: 12
                        },
                        interval: 0
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ccc'
                        }

                    }
                }, {
                    name: '',
                    data: star_value, //顶上x轴数据名称
                    axisLabel: {
                        margin: 7,
                        textStyle: {
                            color: '#2484FF',
                            fontSize: 12
                        },
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ccc',
                            width: 0.5
                        }
                    },
                    nameTextStyle: {
                        color: '#333',
                        fontSize: 14,
                        fontWeight: 400,
                        padding: [10, -30, 0, 0],
                    },
                    splitLine: {
                        show: false,
                        lineStyle: {
                            color: '#E4E4E4',
                            type: 'dashed',
                        },
                    },
                }],
                yAxis: {
                    splitLine: {
                        show: true
                    },
                    splitArea: {
                        show: false
                    },
                    splitNumber: 3,
                    axisLabel: {
                        show: false,
                        textStyle: {
                            color: '#000'
                        },
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#E1EEFF',

                        }
                    }
                },
                series: [{
                    symbolSize: 4,
                    itemStyle: {
                        color: 'rgba(0,0,0,0)',
                        borderColor: '#fff'
                    },
                    data: [
                        [0, star_value[3] / 15],
                        [1, star_value[3] / 15],
                        [2, star_value[3] / 15],
                        [3, star_value[3] / 15]
                    ],
                    type: 'scatter'
                }, {
                    name: 'bar',
                    type: 'bar',
                    showBackground: true,
                    markLine: { //x轴变成虚线(标线)
                        symbol: 'none',
                        label: {
                            show: true,
                            position: 'end',
                            formatter: '',
                            color: '#EA7683',
                        },
                        lineStyle: {
                            type: 'dashed',
                            color: '#2484FF;',
                            width: 1,
                        },
                        data: [{
                            name: '刻度标线',
                            yAxis: 0,
                        }, ],
                    },
                    backgroundStyle: {
                        borderRadius: 6
                    },
                    barWidth: 12,
                    itemStyle: {
                        borderRadius: 6
                    },
                    data: barData
                }]
            };
            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        }
    </script>
</body>

</html>