.b_con{
	padding-top: 10px;
	/*background-color: rgb(91,155,213);*/
	display: flex;
}
.b_con div{
	/*border: 1px solid black;*/
	width: 32%;
	float: left;
	height: 600px;
	box-sizing:border-box;
	border-top: 1px solid #ffffff;
	border-right: 1px solid #ffffff;
	border-left:1px solid #ffffff;
}
.b_con div:nth-child(1){
	background-color: rgb(237,125,49);
}
.b_con div:nth-child(2){
	background-color: rgb(0,127,127);
	margin:0 2%;
}
.b_con div:nth-child(3){
	background-color: rgb(112,173,71);
}
@media screen and (max-width: 767px){/*手机*/
	.b_con div:nth-child(1){
		background-color: rgb(237,125,49);
		width: 1rem;
		height: 50px;
	}
	.b_con div:nth-child(2){
		background-color: rgb(0,127,127);
		margin:0 2%;
		width: 2rem;
	}
	.b_con div:nth-child(3){
		background-color: rgb(112,173,71);
		width: 3rem;
	}
	
}
@media screen and (min-width: 768px)and (max-width:1199px){/* 平板和小屏显示器 */
	.b_con div:nth-child(1){
		background-color: rgb(137,145,49);
	}
	.b_con div:nth-child(2){
		background-color: rgb(212,127,127);
		margin:0 2%;
	}
	.b_con div:nth-child(3){
		background-color: rgb(116,13,71);
	}
}
@media screen and (min-width: 1200px){/* 大屏显示器 */
	.b_con div:nth-child(1){
		background-color: rgb(257,125,49);
		height: 200;
	}
	.b_con div:nth-child(2){
		background-color: rgb(0,12,17);
		margin:0 2%;
	}
	.b_con div:nth-child(3){
		background-color: rgb(12,13,71);
	}
}