<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="./css/base.css">
    <style>
        .nanshan {
            width: 1000px;
            height: 500px;
            /* background-image: url(./image/bg.png); */
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }
    </style>
</head>

<body>
    <svg width="300" height="150">
    <defs>
      <linearGradient id="verticalGrad" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stop-color="#ff9900"/>
        <stop offset="100%" stop-color="#00ccff"/>
      </linearGradient>
    </defs>
    <text x="50%" y="50%" fill="url(#verticalGrad)" 
          font-size="48" text-anchor="middle" dominant-baseline="middle">
      垂直渐变效果
    </text>
  </svg>
    <svg style="display: none">
    <defs>
      <linearGradient id="city-text-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" style="stop-color:#fff;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#ff4500;stop-opacity:1" />
      </linearGradient>
    </defs>
  </svg>

    <div class="nanshan"></div>
    <div style="width: 113px;height: 84px;box-sizing: border-box; border-radius: 2px;background-image: url(./image/center_tooltip.png);background-size: 100% 100%;">
        <div style="padding: 0 12px;line-height: 25px; color: #C0EEFF;"><span style="background-image: linear-gradient(to bottom,#E1F8FF,#6CD7FF);color:transparent;background-clip: text;">西丽</span></div>
        <div style=" padding: 0 12px;">
            <p style="line-height: 28px;"><span style="color: #fff; font-size: 11px;font-weight: 500;">球队</span><span style="font-size: 13px;float: right;background-image: linear-gradient(to bottom,#E1F8FF,#6CD7FF);color:transparent;background-clip: text;">12个</span></p>
            <p style="line-height: 28px;"><span style="color: #fff; font-size: 11px;font-weight: 500;">球队</span><span style="font-size: 13px;float: right;background-image: linear-gradient(to bottom,#E1F8FF,#6CD7FF);color:transparent;background-clip: text;">12个</span></p>

        </div>
    </div>
    <script src="./js/jquery-3.2.1.min.js"></script>
    <script src="https://www.isqqw.com/asset/libs/echarts/5.2.0/echarts.min.js"></script>
    <!-- <script src="https://www.isqqw.com/asset/libs/echarts-gl/1.1.2/echarts-gl.min.js"></script> -->
    <script src="./js/nanshan.js"></script>
</body>

</html>