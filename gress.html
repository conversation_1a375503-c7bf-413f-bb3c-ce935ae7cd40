<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .gress {
            width: 124px;
            display: flex;
            justify-content: space-between;
        }
        
        .gress>span {
            box-sizing: border-box;
            height: 6px;
            width: 12px;
            margin-right: 1px;
            border-radius: 2px;
        }
    </style>
</head>

<body>
    <div class="gress">

    </div>
    <script src="./js/jquery-3.2.1.min.js"></script>
    <script>
        function gress_strang(address, bg_color, begin_color, end_color, gress_num = 0) {
            console.log(bg_color)
            var parseColor = function(hexStr) {
                return hexStr.length === 4 ? hexStr.substr(1).split('').map(function(s) {
                    return 0x11 * parseInt(s, 16);
                }) : [hexStr.substr(1, 2), hexStr.substr(3, 2), hexStr.substr(5, 2)].map(function(s) {
                    return parseInt(s, 16);
                })
            };

            // zero-pad 1 digit to 2
            var pad = function(s) {
                return (s.length === 1) ? '0' + s : s;
            };

            var gradientColors = function(start, end, steps, gamma) {
                var i, j, ms, me, output = [],
                    so = [];
                gamma = gamma || 1;
                var normalize = function(channel) {
                    return Math.pow(channel / 255, gamma);
                };
                start = parseColor(start).map(normalize);
                end = parseColor(end).map(normalize);
                for (i = 0; i < steps; i++) {
                    ms = i / (steps - 1);
                    me = 1 - ms;
                    for (j = 0; j < 3; j++) {
                        so[j] = pad(Math.round(Math.pow(start[j] * me + end[j] * ms, 1 / gamma) * 255).toString(16));
                    }
                    output.push('#' + so.join(''));
                }
                return output;
            };

            // try if it works
            //let colors = gradientColors('#ffffff', '#000000', 11);
            let colors = gradientColors(begin_color, end_color, gress_num)
                // 泥萌的新需求
                // console.log(gradientColors('#000', '#fff', 100));
            var nums = 7;
            let hs = '';
            let gress_bg = bg_color
            for (let i = 0; i < 10; i++) {
                if (i < 7) {
                    hs += `<span style='background: linear-gradient(90deg,${colors[i]},${colors[i + 1]})'></span>`
                } else {
                    hs += `<span style='border:1px solid ${bg_color}'></span>`
                }

            }
            address.html(hs)
        }
        gress_strang($('.gress:eq(0)'), '#000', '#ffffff', '#000000', 11);
        //console.log($('.gress:eq(0)'));
    </script>
</body>

</html>