string_lang_basketball_t=TeamTag Intelligent Basketball Team Data
string_lang_basketball_ft=Basketball Field
string_lang_count_time_t=Timing/Count Data
string_lang_fiels_t=Team Basketball Record List
string_lang_pb_t=Personal Basketball Record
string_lang_pd_t=Personal Data
string_lang_pdr_t=Personal Running Data
string_lang_fr_t=Run Data
string_lang_name=name
string_lang_jersey=jersey

string_lang_assist=assist
string_lang_score=score
string_lang_backboard=backboard
string_lang_tackle=tackle
string_lang_team_data_ba=Team Basketball Data
string_lang_basketball_head=team data summarize-basketball 
string_lang_basketball_step=step
string_lang_basketball_rundis=Run distance(m)
string_lang_distance=distance
string_lang_basketball_heat=Heat(kcal)
string_lang_more=more
string_lang_basketball_fiel=field
string_lang_basketball_tkheight=Take off height
string_lang_basketball_avgmaxh=Avg vertical jump height
string_lang_basketball_maxjh=Maximum vertical jump height
string_lang_basketball_maxtk=Maximum vertical Takeoff
string_lang_basketball_hangtime=Hang time
string_lang_basketball_avgvt=Avg vacating time
string_lang_basketball_maxvt=Maximum time to vacate
string_lang_wx=Microteam 
string_lang_szsy=Shenzhen Laboratory
string_lang_person_head=person data summarize-basketball
string_lang_teamrun=Team run(km)
string_lang_highspeed=High speed
string_lang_midspeed=Medium speed
string_lang_lowspeed=Low speed
string_lang_km=km
string_lang_runals=run analysis
string_lang_person_head=person data summarize
string_lang_person_extime=Exercise time(min)
string_lang_mvp=mvp
string_lang_ranking=ranking
string_lang_maxsprint=Max sprint speed

string_lang_unitm=(unit:m)
string_lang_personrun=person run
string_lang_phyanl=Physical analysis
string_lang_unitkcal=(unit:kcal)
string_lang_pconsume=person consume
string_lang_team_head=Team data summarize
string_lang_avgspeed= Avg speed
string_lang_avgvelocity=Avg velocity
string_lang_avgfrequency=Avg frequency
string_lang_avgstr=Avg stride
string_lang_kmh=km/h
string_lang_stepmin=step/min
string_lang_cm=cm
string_lang_runposture=run posture
string_lang_avgtouchland=Avg touchdown time
string_lang_avglandimp=Avg landing impact
string_lang_avgval=Avg valgus amplitude
string_lang_avgsw=Avg swing
string_lang_touchm=landing mode
string_lang_frontf=Forefoot
string_lang_allf=sole
string_lang_queen=heel
string_lang_pace=pace
string_lang_kmpace=km pace
string_lang_kmtime=km time
string_lang_frequency=frequency
string_lang_fsfre=Fastest step frequency
string_lang_avgfre=Avg step frequency
string_lang_name=name
string_lang_result=result
string_lang_grouprank=Group ranking
string_lang_allranking=Rank all
string_lang_first=champion
string_lang_second=runner-up
string_lang_third=Third
string_lang_allrun= Total run
string_lang_avgrun=Avg run
string_lang_m=m
string_lang_alltime=Total exercise time
string_lang_timedata=Timing Data
string_lang_countdata=Count Data
string_lang_basketballfiel=Basketball Field
string_lang_teamrun_head=Team data summarize-run
string_lang_data_comparison=data comparison
string_lang_comparison=comparison
string_lang_close=close
string_lang_basketball_hover=Avg duration time(ms)
string_lang_basketball_hovermax=Max duration time(ms)
string_lang_basketball_tkdisancemax=Max take off distance
string_lang_basketball_tkheightmax=Max takeoff height
string_lang_basketball_tkheightavg=Avg takeoff height
string_lang_averageheight=Average height
string_lang_maxheight=Max height
string_lang_averagetime=Average time
string_lang_maxtime=Max time
string_lang_agiledata=Agile data
# 变向次数
string_lang_turn=turn
string_lang_turndirection=Number of changges in direction
string_lang_startbraking=Start braking
string_lang_lefttimes=Left Times 
string_lang_righttimes=Right Times
string_lang_numberofstarts=number of starts
string_lang_breakfrequency=Braking frequency
string_lang_Highestsprint=Highest sprint
string_lang_rundistance=Run distance
string_lang_maximumspeed=Maximum speed
string_lang_highsprintspeed=High&Sprint speed
string_lang_velocityinterval=velocity interval distance
string_lang_compare=compare
string_lang_thisperformance=This performance
string_lang_eventlevel=Event Level
string_lang_runnningfitness= Running Fitness
string_lang_offense=Offense
string_lang_guard=Guard
string_lang_fault=Fault
string_lang_turnbackprice=turn-back price
string_lang_worldranking=World Rank
string_lang_rank=rank
string_lang_min=min
string_lang_goal=goal
string_lang_assist=assist
string_lang_pass=pass
string_lang_threatball=threatball
string_lang_preemption=preemption
string_lang_run=run
string_lang_teamcomparsion=Team comparison
string_lang_teamdata=Team Data
string_lang_people=
string_lang_teamrun=Team run distance
string_lang_break=break
string_lang_start=start
string_lang_braking=braking
string_lang_startbraking=start&braking
string_lang_turnleft=Turn Left
string_lang_turnright=Turn Right
string_lang_turnleftright=Turn Left&Right
string_lang_speeddistance=Speed Distance
string_lang_runphsical=Run Physical
string_lang_averageheight=Average height
string_lang_maxheight=Max height
string_lang_averagetime=Average time
string_lang_maxtime=Max time
string_lang_surpass=surpass
string_lang_longpass=long pass
string_lang_wonderfulstop=wonderful stop
string_lang_shoot=shoot
string_lang_threatenshoot=threat shoot
string_lang_scoring=scoring
string_lang_excitingsyeals=exciting syeals
string_lang_headclear=head clear
string_lang_clearance=clear ance
string_lang_wonderful=wonderful
string_lang_passingerror=pass error
string_lang_stoperror=stop error
string_lang_defensiveerror=defensive error
string_lang_waveshooting=waves shoot
string_lang_puking=puking
string_lang_kickair=kickair
string_lang_offside=offside
string_lang_seriouserror=serious error
string_lang_rules=rules
string_lang_yellowcard=yellow card
string_lang_redcard=red card
string_lang_barking=braking
string_lang_distancemin=distance/min
string_lang_teamkcal=Team kcal
string_lang_historykcal=History kcal
string_lang_height=Height
string_lang_weight=Weight
string_lang_jerseynumber=Jersey number
string_lang_playtime=Playing Time
string_lang_turnprice=turn-back price
string_lang_worldrank=World Rankings
string_lang_performance=This performance
string_lang_persondata=person data
string_lang_notes=Notes
string_lang_starcomparison=Star Comparison
string_lang_personrun=Person run
string_lang_agiledata=Agile Data
string_lang_team=Team
string_lang_person=Person
string_lang_abroadleague=Foreign leagues
string_lang_chinaleague=China League
string_lang_china=China
string_lang_abord=Abord
string_lang_onepointer=onePointer
string_lang_twopointer=twoPointer
string_lang_threepointer=threePointer
string_lang_nullone=nullone
string_lang_nulltwo=nulltwo
string_lang_nullthree=nullthree
string_lang_treebasket=treeBasket
string_lang_assist=assist
string_lang_backboard=backboard
string_lang_snach=snatch
string_lang_blackshot=blockShot
string_lang_walk=walk
string_lang_pullpeople=pullPeople
string_lang_walkball=walkBall
string_lang_threeviolation=threeViolation
string_lang_illegaldribble=illegalDribble
string_lang_intentionaball=intentionalBall
string_lang_hookfoul=hookFoul
string_lang_dribbler=dribbler
string_lang_outball=outBall
string_lang_illegalhands=illegalHands
string_lang_headfoul=headFoul
string_lang_flyingelbow=flyingElbow
string_lang_illegalattack=illegalAttack
string_lang_illegaldefense=illegalDefense
string_lang_pushpeople=pushPepole
string_lang_penaltyexit=penaltyExit
string_lang_technicalfoul=technicalfoul
string_lang_violationfoul=violationfoul
string_lang_comeback=comeback
string_lang_amazing=amazing
string_lang_nicepass=nicepass
string_lang_dunk=dunk
string_lang_helpball=helpball
string_lang_frontback=frontback
string_lang_afterback=afterback
string_lang_treebasketball=treeBasketball
