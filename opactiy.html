<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化的文字动画效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }
        
        .animation_word {
            width: 400px;
            min-height: 300px;
            max-height: 500px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            overflow-y: auto;
            padding: 25px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            transition: all 0.3s ease;
        }
        
        .animation_word:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .animation_word::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            border-radius: 15px 15px 0 0;
        }
        
        .animation_word span {
            color: transparent;
            opacity: 0;
            font-size: 16px;
            line-height: 24px;
            font-weight: 400;
            margin-right: 2px;
            display: inline-block;
            transition: all 0.3s ease;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
        }
        
        .animation_word span.visible {
            color: #ffffff;
            opacity: 1;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn.primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }
        
        .settings {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 25px;
            border-radius: 15px;
            backdrop-filter: blur(5px);
        }
        
        .setting-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        
        .setting-group label {
            color: white;
            font-size: 12px;
            font-weight: 500;
        }
        
        .setting-group input {
            width: 80px;
            padding: 5px 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-align: center;
            font-size: 12px;
        }
        
        .setting-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .status {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            text-align: center;
            min-height: 20px;
        }
        /* 响应式设计 */
        
        @media (max-width: 768px) {
            .animation_word {
                width: 90vw;
                max-width: 350px;
            }
            .controls {
                flex-direction: column;
                width: 100%;
            }
            .btn {
                width: 100%;
                max-width: 200px;
            }
            .settings {
                flex-direction: column;
                gap: 15px;
            }
        }
        /* 滚动条样式 */
        
        .animation_word::-webkit-scrollbar {
            width: 6px;
        }
        
        .animation_word::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }
        
        .animation_word::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }
        
        .animation_word::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 状态显示 -->
        <div class="status" id="status">准备开始...</div>

        <!-- 动画显示区域 -->
        <div class="animation_word" id="animationContainer"></div>

        <!-- 控制按钮 -->
        <div class="controls">
            <button class="btn primary" id="startBtn">开始动画</button>
            <button class="btn secondary" id="pauseBtn">暂停</button>
            <button class="btn" id="resetBtn">重置</button>
            <button class="btn" id="nextBtn">下一段</button>
        </div>

        <!-- 设置面板 -->
        <div class="settings">
            <div class="setting-group">
                <label>字符间隔(ms)</label>
                <input type="number" id="charSpeed" value="80" min="10" max="500">
            </div>
            <div class="setting-group">
                <label>淡入速度(ms)</label>
                <input type="number" id="fadeSpeed" value="30" min="10" max="100">
            </div>
            <div class="setting-group">
                <label>段落间隔(s)</label>
                <input type="number" id="paragraphDelay" value="3" min="1" max="10">
            </div>
        </div>
    </div>

    <script>
        // 优化后的文字动画类
        class TextAnimator {
            constructor(container, options = {}) {
                this.container = container;
                this.config = {
                    charSpeed: options.charSpeed || 80,
                    fadeSpeed: options.fadeSpeed || 30,
                    paragraphDelay: options.paragraphDelay || 3000,
                    fadeSteps: 10,
                    autoLoop: options.autoLoop !== false
                };

                // 默认文本数据
                this.textArray = [
                    "欢迎来到优化的文字动画效果演示！这是一个全新设计的文字逐字显示动画系统。",
                    "这个系统具有流畅的动画效果、可自定义的参数设置、以及完善的控制功能。",
                    "您可以通过下方的控制按钮来操作动画，也可以调整各种参数来获得不同的效果。",
                    "感谢您的使用，希望这个动画效果能够满足您的需求！"
                ];

                this.currentIndex = 0;
                this.isPlaying = false;
                this.isPaused = false;
                this.currentAnimation = null;
                this.currentCharIndex = 0;
                this.currentText = '';

                this.init();
            }

            init() {
                this.bindEvents();
                this.updateStatus('准备就绪');
            }

            // 绑定事件
            bindEvents() {
                const startBtn = document.getElementById('startBtn');
                const pauseBtn = document.getElementById('pauseBtn');
                const resetBtn = document.getElementById('resetBtn');
                const nextBtn = document.getElementById('nextBtn');

                const charSpeedInput = document.getElementById('charSpeed');
                const fadeSpeedInput = document.getElementById('fadeSpeed');
                const paragraphDelayInput = document.getElementById('paragraphDelay');

                startBtn.addEventListener('click', () => this.start());
                pauseBtn.addEventListener('click', () => this.togglePause());
                resetBtn.addEventListener('click', () => this.reset());
                nextBtn.addEventListener('click', () => this.next());

                // 参数变化监听
                charSpeedInput.addEventListener('change', (e) => {
                    this.config.charSpeed = parseInt(e.target.value);
                });

                fadeSpeedInput.addEventListener('change', (e) => {
                    this.config.fadeSpeed = parseInt(e.target.value);
                });

                paragraphDelayInput.addEventListener('change', (e) => {
                    this.config.paragraphDelay = parseInt(e.target.value) * 1000;
                });
            }

            // 开始动画
            start() {
                if (this.isPaused) {
                    this.resume();
                    return;
                }

                if (this.isPlaying) {
                    this.updateStatus('动画已在运行中...');
                    return;
                }

                this.isPlaying = true;
                this.currentIndex = 0;
                this.animateText(this.textArray[this.currentIndex]);
            }

            // 暂停/恢复
            togglePause() {
                if (!this.isPlaying) {
                    this.updateStatus('请先开始动画');
                    return;
                }

                if (this.isPaused) {
                    this.resume();
                } else {
                    this.pause();
                }
            }

            pause() {
                this.isPaused = true;
                if (this.currentAnimation) {
                    clearTimeout(this.currentAnimation);
                }
                this.updateStatus('动画已暂停');
                document.getElementById('pauseBtn').textContent = '继续';
            }

            resume() {
                this.isPaused = false;
                this.updateStatus('动画继续中...');
                document.getElementById('pauseBtn').textContent = '暂停';

                // 继续当前文本的动画
                if (this.currentText && this.currentCharIndex < this.currentText.length) {
                    this.continueAnimation();
                }
            }

            // 重置
            reset() {
                this.stop();
                this.container.innerHTML = '';
                this.currentIndex = 0;
                this.currentCharIndex = 0;
                this.currentText = '';
                this.updateStatus('已重置');
            }

            // 下一段
            next() {
                if (!this.isPlaying) {
                    this.updateStatus('请先开始动画');
                    return;
                }

                this.stop();
                this.currentIndex = (this.currentIndex + 1) % this.textArray.length;
                this.container.innerHTML = '';
                this.animateText(this.textArray[this.currentIndex]);
            }

            // 停止当前动画
            stop() {
                if (this.currentAnimation) {
                    clearTimeout(this.currentAnimation);
                    this.currentAnimation = null;
                }
                this.isPlaying = false;
                this.isPaused = false;
                document.getElementById('pauseBtn').textContent = '暂停';
            }

            // 动画文本
            animateText(text) {
                this.currentText = text;
                this.currentCharIndex = 0;
                this.updateStatus(`正在显示第 ${this.currentIndex + 1} 段文本...`);
                this.animateNextChar();
            }

            // 动画下一个字符
            animateNextChar() {
                if (this.isPaused) return;

                if (this.currentCharIndex >= this.currentText.length) {
                    this.onTextComplete();
                    return;
                }

                const char = this.currentText[this.currentCharIndex];
                const span = this.createCharSpan(char);
                this.container.appendChild(span);

                this.animateCharFadeIn(span);
                this.currentCharIndex++;

                this.currentAnimation = setTimeout(() => {
                    this.animateNextChar();
                }, this.config.charSpeed);
            }

            // 继续动画（用于暂停后恢复）
            continueAnimation() {
                this.currentAnimation = setTimeout(() => {
                    this.animateNextChar();
                }, this.config.charSpeed);
            }

            // 创建字符元素
            createCharSpan(char) {
                const span = document.createElement('span');
                span.textContent = char;
                span.style.opacity = '0';
                span.style.color = 'transparent';
                return span;
            }

            // 字符淡入动画
            animateCharFadeIn(span) {
                let opacity = 0;
                const fadeInterval = setInterval(() => {
                    opacity++;
                    span.style.opacity = opacity / this.config.fadeSteps;

                    if (opacity >= this.config.fadeSteps) {
                        clearInterval(fadeInterval);
                        span.classList.add('visible');
                        span.style.color = '#ffffff';
                        span.style.opacity = '1';
                    }
                }, this.config.fadeSpeed);
            }

            // 文本完成回调
            onTextComplete() {
                this.updateStatus(`第 ${this.currentIndex + 1} 段完成`);

                if (this.config.autoLoop) {
                    this.currentAnimation = setTimeout(() => {
                        this.currentIndex = (this.currentIndex + 1) % this.textArray.length;

                        if (this.currentIndex === 0) {
                            this.updateStatus('一轮循环完成，重新开始...');
                        }

                        this.container.innerHTML = '';
                        this.animateText(this.textArray[this.currentIndex]);
                    }, this.config.paragraphDelay);
                } else {
                    this.isPlaying = false;
                    this.updateStatus('动画完成');
                }
            }

            // 更新状态显示
            updateStatus(message) {
                const statusElement = document.getElementById('status');
                if (statusElement) {
                    statusElement.textContent = message;
                }
            }

            // 设置新的文本数组
            setTextArray(textArray) {
                this.textArray = textArray;
                this.reset();
            }

            // 获取当前配置
            getConfig() {
                return {...this.config
                };
            }

            // 更新配置
            updateConfig(newConfig) {
                this.config = {...this.config,
                    ...newConfig
                };
            }
        }

        // 初始化动画器
        const container = document.getElementById('animationContainer');
        const animator = new TextAnimator(container, {
            charSpeed: 80,
            fadeSpeed: 30,
            paragraphDelay: 3000,
            autoLoop: true
        });

        // 页面加载完成后自动开始
        window.addEventListener('load', () => {
            setTimeout(() => {
                animator.start();
            }, 1000);
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                if (animator.isPlaying && !animator.isPaused) {
                    animator.pause();
                }
            }
        });
    </script>
</body>

</html>