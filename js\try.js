var a=[3,6,7];
a['p']=2;
//console.log(a.length);
for (var i = 0; i < a.length; i++) {
	console.log(a[i]);
}
var b=[2,,4];
var c=[,,]
console.log(c);
var d=['kill','death','what','cat'];
d.sort(function(s,t){
	var a=s.toLowerCase();
	var b=t.toLowerCase();
	if (a<b) return -1;
	if (a>b) return 1;
	return 0;
})
console.log(d);
var sum=0;

a.forEach(function(value,ib,a){
	//sum+=value;
	a[ib]=value+3;
})
console.log(a);
var bma=a.map(function(x){
	return x*x;
})
console.log(bma);
var newa=[5,8,75,69,45,14];
var newb=newa.filter(function(a){
	return a>50;
})
console.log(newb);
var er=[1,2,5,8];
var ere=er.every(function(x){
	return x<10;
})
console.log(ere);
var erd=er.some(function(x){
	return x>10;
})
console.log(erd);
var gr=[1,5,6];
var dcu=gr.reduce(function(x,y){//x第一个参数是到目前为止的化简操作累计的结果其他3个参数和map一样
	return x+y;
},0)
console.log(dcu);
var ids=[1,2,8,7,6,2];
var huang=ids.indexOf(6);
var dao=ids.lastIndexOf(2);
console.log(dao);
console.log(huang);
console.log(Array.isArray(ids));

var bds=function (){
	return 1;
}
console.log(bds());
var nt={
	a:1,
	hu:2,
	name:"huanghuang"
}
function getPropertyNames(o,a){
	a=a||[];
	for(var item in o){
		a.push(item)
	}
	return a;
}
console.log(getPropertyNames(nt));
function arraycopy(/*array*/from,/*index*/from_start,/*array*/to,/*index*/to_start,/*integer*/length){
	for (var i = 0; i < length;i++) {
		to[to_start]=from[from_start+i];
		to_start++;
	}
	return to;
}
function easycopy(args){
	return arraycopy(args.from,args.from_start||0,args.to,args.to_start||0,args.length);
	
}
var arrGry=[1,2,3,4];
var arrBry=[];
console.log(easycopy({from:arrGry,to:arrBry,length:2}));
console.log(arrBry);

var scope="global socpe";
function checkscope(){
	var scope="local scope";
	function f(){ return scope};
	return f();
}
console.log(checkscope());
var sc="global socpe";
function check(){
	var sc="local scope";
	function f(){return sc};
	return f;
}
console.log(check()());

function chs(args){
	var actual=args.length;
	var except=args.callee.length;
	if (actual!==except) {
		throw Error('错误');
	}else{
		console.log('正确');
	}
}
function f(x,y,z){
	chs(arguments);
	return x+y;
}
console.log(f(1,2,2));
function bing(y){
	return this.x+y;
}
var good={x:3};
var gds=bing.bind(good);
console.log(gds(3));
var a=[1,5,2,3,4,7];
console.log(a.length);
// function array(a,n){
// 	return Array.prototype.slice.call(a,n||0);
// }
// function partialLeft(f){
// 	var args=arguments;
// 	return function(){
// 		var a=array(args,1);
// 		a=a.concat(array(arguments))
// 		return f.apply(this,a);
// 	}

// }
// var f=function(x,y,z){
// 	return x*(y-z);
// }
// console.log(array());
// 
function trace(o,m){
	var original=o[m];
	console.log(o[m]);
	o[m]=function () {
		console.log(new Date(),"Entreing:",m);
		console.log(this);
		var result=original.apply(this,arguments);
		console.log(new Date(),"exiting:",m);
		console.log(this);
		return result;
	}
}
var be={
	be:5,
	m:function () {
		console.log(this);
		console.log(this==be);
		return 22;
		
	},
}
var hhh=trace(be,'m');
console.log(be.m('54848484'));
function so(){
	var original=
	m=function(){
		console.log(this);
		
	}
	
}
var sou=so();