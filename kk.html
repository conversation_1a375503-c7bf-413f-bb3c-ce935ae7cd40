
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no">
    <meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1'/>
    <!-- <link rel="stylesheet" href="http://v3.faqrobot.org/hvb/com/css/reset.css?dev=1"> -->
 
    <script type="text/javascript" src="js/jquery-3.2.1.min.js"></script>
    <!-- <script type="text/javascript" src="http://v3.faqrobot.org/hvb/com/js/base.js?dev=1"></script> -->
    <title>svg</title>
 
    <style>
        body, html {
            width: 100%;
            height: 100%;
            font-size: 0;
            background: #000;
        }
        .circle {
            background: #fff;
            width: 2px;
            height: 2px;
            border-radius: 100px;
            position: absolute;
        }
        
 
    </style>
</head>
<body>
    
<script>
    ;$(function() {
        var body = document.querySelector('body');
 
        body.appendChild(createCircle(30, 200));
        var cur_x = 30;//初始水平坐标
        var cur_y = 200;//初始垂直坐标
 
        var unit_x = 5;//固定水平单元距离
        var unit_y = 30;//最大垂直单元距离
        var angle = 0;//角度
        var dis = 0;//随机水平距离
        var point_x = cur_x+dis;//目标位置
 
 
 
        var canDraw = true;//是否可画
        setInterval(function() {
            canDraw = false;
            if(cur_x >= point_x) {//开始下一个随机点
                            console.log(cur_y);
 
                angle = Math.round(Math.random()*2);
                switch(angle) {
                    case 0://上
                        angle=30;
                        break;
                    case 1://水平
                        angle=0;
                        break;
                    case 2://下
                        angle=-30;
                        break;
                }
                dis = Math.round(Math.random()*30+10);
                point_x = cur_x+dis;
            }else {
                switch(angle) {
                    case 30://上
                        if(cur_x < point_x-dis/2) {
                            cur_y -= parseInt((dis - (point_x-cur_x))*Math.tan(angle*Math.PI/180));
                        }else {
                            cur_y += parseInt(((point_x-cur_x))*Math.tan(angle*Math.PI/180));
                        }
                        body.appendChild(createCircle(cur_x, cur_y));
                        cur_x += 0.5;
                        break;
                    case 0://水平
                        body.appendChild(createCircle(cur_x, cur_y));
                        cur_x += 3;
                        break;
                    case -30://下
                        if(cur_x < point_x-dis/2) {
                            cur_y -= parseInt((dis - (point_x-cur_x))*Math.tan(angle*Math.PI/180));
                        }else {
                            cur_y += parseInt(((point_x-cur_x))*Math.tan(angle*Math.PI/180));
                        }
                        body.appendChild(createCircle(cur_x, cur_y));
                        cur_x += 0.5;
                        break;
                }
            }
        }, 15);
 
        
 
        function createCircle(x, y) {
            var circle = document.createElement('div');
            circle.setAttribute('class', 'circle');
            circle.style.left = x +'px';
            circle.style.top = y +'px';
            return circle;
        }
    });
</script>
</body>
</html>