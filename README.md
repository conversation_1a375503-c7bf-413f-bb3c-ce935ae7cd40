# 简化版文字动画函数

一个简单易用的文字逐字显示动画函数，只需要传入文字数组、淡入速度和段落间隔时间即可创建流畅的文字动画效果。

## 特性

- ✅ **超简单调用**：只需3个参数即可使用
- ✅ **流畅动画**：字符逐个淡入显示
- ✅ **自动循环**：支持多段文字自动轮播
- ✅ **完整控制**：提供开始、暂停、恢复、停止等方法
- ✅ **响应式设计**：适配各种屏幕尺寸
- ✅ **零依赖**：纯JavaScript实现，无需任何库

## 快速开始

### 1. 基础用法

```javascript
// 准备文字数组
const textArray = [
    "欢迎来到文字动画效果！",
    "这是第二段文字内容。",
    "这是第三段文字内容。"
];

// 创建动画（超简单！）
const animation = createTextAnimation(
    '#container',    // 容器选择器
    textArray,       // 文字数组
    30,             // 淡入速度(毫秒)
    3               // 段落间隔(秒)
);
```

### 2. HTML结构

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>文字动画示例</title>
    <style>
        #container {
            width: 400px;
            height: 300px;
            padding: 20px;
            background: #333;
            color: white;
            font-size: 16px;
            line-height: 24px;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    
    <script src="text-animation.js"></script>
    <script>
        const textArray = ["你好世界！", "这是一个简单的文字动画。"];
        const animation = createTextAnimation('#container', textArray, 30, 3);
    </script>
</body>
</html>
```

## API 参考

### createTextAnimation(container, textArray, fadeSpeed, paragraphDelay, options)

#### 参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `container` | string\|HTMLElement | - | 容器选择器或DOM元素 |
| `textArray` | Array | - | 文字数组 |
| `fadeSpeed` | number | 30 | 字符淡入速度(毫秒) |
| `paragraphDelay` | number | 3 | 段落间隔时间(秒) |
| `options` | Object | {} | 其他可选配置 |

#### options 配置项

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `charSpeed` | number | 80 | 字符显示间隔(毫秒) |
| `fadeSteps` | number | 10 | 淡入步数 |
| `autoLoop` | boolean | true | 是否自动循环 |
| `autoStart` | boolean | true | 是否自动开始 |

#### 返回值

返回一个控制对象，包含以下方法：

| 方法 | 描述 | 返回值 |
|------|------|--------|
| `start()` | 开始动画 | api对象 |
| `pause()` | 暂停动画 | api对象 |
| `resume()` | 恢复动画 | api对象 |
| `stop()` | 停止动画 | api对象 |
| `next()` | 跳到下一段 | api对象 |
| `setTextArray(array)` | 设置新的文字数组 | api对象 |
| `updateConfig(config)` | 更新配置 | api对象 |
| `getStatus()` | 获取当前状态 | 状态对象 |
| `getConfig()` | 获取当前配置 | 配置对象 |

## 使用示例

### 示例1：基础使用

```javascript
const animation = createTextAnimation(
    '#myContainer',
    ['第一段', '第二段', '第三段'],
    30,  // 30毫秒淡入速度
    3    // 3秒段落间隔
);
```

### 示例2：自定义配置

```javascript
const animation = createTextAnimation(
    document.getElementById('container'),
    ['文字内容1', '文字内容2'],
    20,  // 更快的淡入速度
    5,   // 更长的段落间隔
    {
        charSpeed: 60,      // 字符间隔60毫秒
        autoLoop: false,    // 不自动循环
        autoStart: false    // 不自动开始
    }
);

// 手动开始
animation.start();
```

### 示例3：控制动画

```javascript
const animation = createTextAnimation('#container', textArray, 30, 3);

// 暂停动画
animation.pause();

// 恢复动画
animation.resume();

// 跳到下一段
animation.next();

// 停止动画
animation.stop();

// 更换文字内容
animation.setTextArray(['新的文字1', '新的文字2']);
```

### 示例4：获取状态

```javascript
const animation = createTextAnimation('#container', textArray, 30, 3);

// 获取当前状态
const status = animation.getStatus();
console.log(status);
// 输出：
// {
//     isPlaying: true,
//     isPaused: false,
//     currentIndex: 0,
//     currentCharIndex: 15,
//     totalTexts: 3,
//     currentText: "当前正在显示的文字"
// }
```

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ IE 11+ (需要polyfill)

## 许可证

MIT License

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的文字动画功能
- 提供完整的控制API
