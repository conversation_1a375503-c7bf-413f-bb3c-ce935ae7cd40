var uploadedDataURL = "./json/na.json";
// var bg = '<div class="bg-tooltip"></div>'
var myChart = echarts.init(document.getElementsByClassName('nanshan')[0]);
var uploadnanshan = './json/440305.geoJson';
$.getJSON(uploadnanshan, function(geonanshanJson) {
    //echarts.registerMap('ns', geonanshanJson);
    $.getJSON(uploadedDataURL, function(geoJson) {
        echarts.registerMap('nanshan', geoJson);
        let geoCoordMap = {
                '塘朗': [113.999719, 22.603784],
                '南头': [113.91171099999996, 22.551691],
                '南山': [113.88559699999999, 22.498413],
                '西丽': [113.945385, 22.622042],
                '沙河': [113.9768, 22.5362],
                '蛇口': [113.9235, 22.4863],
                '招商': [113.90036699999993, 22.480851],
                '高新': [113.93729200000007, 22.545173],
                '粤海': [113.92808299999999, 22.516687],
                '桃源': [113.96945000000004, 22.567461],
                '深湾': [113.93860400000004, 22.502813]

            }
            // 生成数据的函数
        function generateData() {
            var regions = ['塘朗', '南头', '南山', '西丽', '沙河', '蛇口', '招商', '高新', '粤海', '桃源', '深湾'];
            var data = [];
            var teamData = []; // 球队数据数组
            var playerData = []; // 球员数据数组

            regions.forEach(function(region) {
                data.push({
                    name: region,
                    value: Math.floor(Math.random() * 400) + 50 // 50-450之间的随机数
                });

                teamData.push({
                    name: region,
                    value: 0 // 球队数据设置为0
                });

                playerData.push({
                    name: region,
                    value: 0 // 球员数据设置为0
                });
            });

            return {
                data: data,
                teamData: teamData,
                playerData: playerData
            };
        }

        var dataResult = generateData();
        var data = dataResult.data;
        var teamData = dataResult.teamData;
        var playerData = dataResult.playerData;
        var max = 480,
            min = 9; // todo
        var maxSize4Pin = 100,
            minSize4Pin = 20;

        var convertData = function(data) {
            //console.log(data)
            var res = [];
            for (var i = 0; i < data.length; i++) {
                var geoCoord = geoCoordMap[data[i].name];
                if (geoCoord) {
                    res.push({
                        name: data[i].name,
                        //value: data[i].value,
                        value: geoCoord.concat(data[i].value)
                    });
                }
            }
            console.log(res)
            return res;
        };
        option = {
            //backgroundColor: '#013954',


            tooltip: {
                trigger: 'item',
                backgroundColor: 'rgba(0, 57, 84, 0.95)',
                borderColor: '#2ab8ff',
                borderWidth: 1,
                borderRadius: 6,
                padding: [0, 0],
                textStyle: {
                    color: '#fff',
                    fontSize: 12
                },
                formatter: function(params) {
                    if (params.componentType === 'geo' || params.seriesType === 'map') {
                        // 获取对应地区的球队和球员数据
                        var regionName = params.name;
                        var teamCount = 0;
                        var playerCount = 0;

                        // 查找对应地区的数据
                        for (var i = 0; i < teamData.length; i++) {
                            if (teamData[i].name === regionName) {
                                teamCount = teamData[i].value;
                                break;
                            }
                        }

                        for (var j = 0; j < playerData.length; j++) {
                            if (playerData[j].name === regionName) {
                                playerCount = playerData[j].value;
                                break;
                            }
                        }

                        return `<div style="width: 113px;height: 84px;box-sizing: border-box; border-radius: 2px;background-image: url(./image/center_tooltip.png);background-size: 100% 100%;">
                            <div style="padding: 0 12px;line-height: 25px; color: #C0EEFF;"><span style="background-image: linear-gradient(to bottom,#E1F8FF,#6CD7FF);color:transparent;background-clip: text;">${regionName}</span></div>
                            <div style=" padding: 0 12px;">
                            <p style="line-height: 28px;"><span style="color: #fff; font-size: 11px;font-weight: 500;">球队</span><span style="font-size: 13px;float: right;background-image: linear-gradient(to bottom,#E1F8FF,#6CD7FF);color:transparent;background-clip: text;">${teamCount}个</span></p>
                            <p style="line-height: 28px;"><span style="color: #fff; font-size: 11px;font-weight: 500;">球员</span><span style="font-size: 13px;float: right;background-image: linear-gradient(to bottom,#E1F8FF,#6CD7FF);color:transparent;background-clip: text;">${playerCount}人</span></p>
                            </div>
                            </div>`
                    } else {
                        if (typeof(params.value)[2] == "undefined") {
                            return params.name + ' : ' + params.value;
                        } else {
                            return params.name + ' : ' + params.value[2];
                        }
                    }
                }
            },

            visualMap: {
                show: false,
                hoverLink: false,
                min: 0,
                max: 500,
                left: 'left',
                top: 'bottom',
                text: ['高', '低'], // 文本，默认为数值文本
                calculable: true,
                seriesIndex: [1],
                inRange: {
                    color: ['#1488CC', '#2B32B2'] // 浅蓝
                }
            },
            geo: [{
                show: true,
                // zoom: 1.1,
                map: 'nanshan',
                aspectScale: 0.75,
                layoutCenter: ['60%', '50.5%'],
                layoutSize: '120%',
                silent: true,
                roam: false,
                z: 0,
                itemStyle: {
                    normal: {
                        color: '#fff',
                        areaColor: {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 500,
                            colorStops: [{
                                    offset: 0,
                                    color: "rgba(3,27,78,0.75)", // 0% 处的颜色
                                },
                                {
                                    offset: 1,
                                    color: "rgba(58,149,253,0.75)", // 50% 处的颜色
                                },
                            ],
                            global: true, // 缺省为 false
                        },

                        shadowColor: 'rgba(24, 132, 214, 1)',
                        shadowBlur: 2,
                        shadowOffsetX: 2,
                        shadowOffsetY: 2,
                        borderColor: 'rgba(129, 255, 255, 1)',
                        borderWidth: 1,
                    },
                    emphasis: {
                        areaColor: '#0469b6',
                        borderWidth: 1,
                        color: '#fff',
                        label: {
                            show: false,
                        },
                    },
                },
            }, {
                show: true,
                // zoom: 1.1,
                zlevel: -1,
                map: 'nanshan',
                aspectScale: 0.75,
                layoutCenter: ['60%', '50.5%'],
                layoutSize: '120%',
                silent: true,
                roam: false,
                z: 0,
                itemStyle: {
                    normal: {
                        color: '#fff',
                        areaColor: {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 500,
                            colorStops: [{
                                    offset: 0,
                                    color: "rgba(3,27,78,0.75)", // 0% 处的颜色
                                },
                                {
                                    offset: 1,
                                    color: "rgba(58,149,253,0.75)", // 50% 处的颜色
                                },
                            ],
                            global: true, // 缺省为 false
                        },

                        shadowColor: 'rgb(0,0,0,0)',
                        shadowBlur: 10,
                        shadowOffsetX: 5,
                        shadowOffsetY: 10,
                        borderColor: 'rgba(132, 255, 255, 0)',
                        borderWidth: 0.5,
                    },
                    emphasis: {
                        areaColor: '#0469b6',
                        borderWidth: 1,
                        color: '#fff',
                        label: {
                            show: false,
                        },
                    },
                },
            }],
            series: [{
                    name: 'credit_pm2.5',
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    data: convertData(data),
                    symbolSize: function(val) {
                        //return val[2] / 30;
                        return 10;
                    },
                    label: {
                        normal: {
                            color: '#fff',
                            formatter: '{b}',
                            position: 'right',
                            show: true
                        },
                        emphasis: {
                            show: true
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: '#fff',
                        }
                    }
                },
                {
                    type: 'map',
                    map: 'nanshan',
                    layoutCenter: ['60%', '50.5%'],
                    layoutSize: '120%',
                    roam: true,

                    itemStyle: {
                        normal: {
                            borderColor: 'rgba(255, 255, 255, 0.5)',
                            borderWidth: 0.5,
                            areaColor: {
                                type: "linear",
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 500,
                                colorStops: [{
                                        offset: 0,
                                        color: "rgba(3,27,78,0.75)", // 0% 处的颜色
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(58,149,253,0.75)", // 50% 处的颜色
                                    },
                                ],
                                global: true, // 缺省为 false
                            },
                            shadowColor: 'rgba(0, 0, 0, 0.5)',
                            shadowBlur: 0,
                            shadowOffsetX: 0,
                            shadowOffsetY: 1,
                        },
                        emphasis: {
                            // areaColor: {
                            //     //image: domImgHover,
                            //     repeat: 'repeat',
                            // },
                            areaColor: '#0469b6',
                            borderColor: '#2ab8ff',
                            borderWidth: 1,
                            shadowColor: 'rgba(0, 255, 255, 0.7)',
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowOffsetY: 1,
                            label: {
                                show: false,
                            },
                        },

                    },
                    zoom: 1,
                    roam: false,

                },

                {
                    name: 'Top 5',
                    type: 'effectScatter',
                    coordinateSystem: 'geo',
                    data: convertData(data.sort(function(a, b) {
                        return b.value - a.value;
                    }).slice(0, 5)),
                    symbolSize: function(val) {
                        return val[2] / 30;
                    },
                    showEffectOn: 'render',
                    rippleEffect: {
                        brushType: 'stroke'
                    },
                    hoverAnimation: true,
                    label: {
                        normal: {

                            formatter: '{b}',
                            position: 'right',
                            show: false
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: '#46A7FF',
                            shadowBlur: 10,
                            shadowColor: '#05C3F9'
                        }
                    },
                    zlevel: 1
                },

            ]
        };
        myChart.setOption(option);

        // 优化后的轮换高亮显示功能
        var MapRotationManager = {
            regions: ['塘朗', '南头', '南山', '西丽', '沙河', '蛇口', '招商', '高新', '粤海', '桃源', '深湾'],
            currentIndex: 0,
            highlightTimer: null,
            isUserInteracting: false,
            restartTimer: null,

            // 配置参数
            config: {
                rotationInterval: 3000, // 轮换间隔
                restartDelay: 500, // 重启延迟
                initialDelay: 1000 // 初始延迟
            },

            // 初始化
            init: function() {
                this.start();
                this.bindEvents();
            },

            // 开始轮换
            start: function() {
                var self = this;

                // 清除现有定时器
                this.stop();

                // 初始延迟后开始第一次显示
                setTimeout(function() {
                    if (!self.isUserInteracting) {
                        self.showCurrentRegion();
                        self.currentIndex = (self.currentIndex + 1) % self.regions.length;

                        // 开始定时轮换
                        self.highlightTimer = setInterval(function() {
                            if (!self.isUserInteracting) {
                                self.rotateToNext();
                            }
                        }, self.config.rotationInterval);
                    }
                }, this.config.initialDelay);
            },

            // 停止轮换
            stop: function() {
                if (this.highlightTimer) {
                    clearInterval(this.highlightTimer);
                    this.highlightTimer = null;
                }
                if (this.restartTimer) {
                    clearTimeout(this.restartTimer);
                    this.restartTimer = null;
                }
            },

            // 轮换到下一个地区
            rotateToNext: function() {
                // 清除当前状态
                this.clearCurrentState();

                // 显示新地区
                this.showCurrentRegion();

                // 更新索引
                this.currentIndex = (this.currentIndex + 1) % this.regions.length;
            },

            // 显示当前地区
            showCurrentRegion: function() {
                var regionName = this.regions[this.currentIndex];

                // 高亮地区
                myChart.dispatchAction({
                    type: 'highlight',
                    seriesIndex: 1,
                    name: regionName
                });

                // 显示tooltip
                myChart.dispatchAction({
                    type: 'showTip',
                    seriesIndex: 1,
                    name: regionName
                });
            },

            // 清除当前状态
            clearCurrentState: function() {
                myChart.dispatchAction({
                    type: 'downplay',
                    seriesIndex: 1
                });
                myChart.dispatchAction({
                    type: 'hideTip'
                });
            },

            // 用户交互开始
            onUserInteractionStart: function() {
                this.isUserInteracting = true;
                this.stop();
                this.clearCurrentState();
            },

            // 用户交互结束
            onUserInteractionEnd: function() {
                var self = this;
                this.isUserInteracting = false;

                // 延迟重启，避免频繁切换
                this.restartTimer = setTimeout(function() {
                    if (!self.isUserInteracting) {
                        self.start();
                    }
                }, this.config.restartDelay);
            },

            // 绑定事件
            bindEvents: function() {
                var self = this;

                // 鼠标进入地图
                myChart.on('mouseover', function(params) {
                    if (params.componentType === 'geo' || params.seriesType === 'map') {
                        self.onUserInteractionStart();
                    }
                });

                // 鼠标离开地图
                myChart.on('mouseout', function(params) {
                    if (params.componentType === 'geo' || params.seriesType === 'map') {
                        self.onUserInteractionEnd();
                    }
                });

                // 鼠标离开整个图表容器
                myChart.getDom().addEventListener('mouseleave', function() {
                    self.onUserInteractionEnd();
                });

                // 窗口失焦时暂停
                window.addEventListener('blur', function() {
                    self.onUserInteractionStart();
                });

                // 窗口获焦时恢复
                window.addEventListener('focus', function() {
                    self.onUserInteractionEnd();
                });
            }
        };

        // 添加性能优化和错误处理
        var PerformanceOptimizer = {
            // 防抖函数
            debounce: function(func, wait) {
                var timeout;
                return function() {
                    var context = this;
                    var args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(function() {
                        func.apply(context, args);
                    }, wait);
                };
            },

            // 节流函数
            throttle: function(func, limit) {
                var inThrottle;
                return function() {
                    var args = arguments;
                    var context = this;
                    if (!inThrottle) {
                        func.apply(context, args);
                        inThrottle = true;
                        setTimeout(function() {
                            inThrottle = false;
                        }, limit);
                    }
                };
            }
        };

        // 错误处理和日志
        var ErrorHandler = {
            log: function(message, error) {
                if (console && console.warn) {
                    console.warn('[地图轮换] ' + message, error || '');
                }
            },

            safeExecute: function(func, context, errorMessage) {
                try {
                    return func.call(context);
                } catch (error) {
                    this.log(errorMessage || '执行出错', error);
                    return null;
                }
            }
        };

        // 优化MapRotationManager的事件绑定
        MapRotationManager.bindEvents = function() {
            var self = this;

            // 使用节流优化鼠标事件
            var throttledMouseOver = PerformanceOptimizer.throttle(function(params) {
                if (params.componentType === 'geo' || params.seriesType === 'map') {
                    ErrorHandler.safeExecute(function() {
                        self.onUserInteractionStart();
                    }, self, '鼠标进入事件处理出错');
                }
            }, 100);

            var debouncedMouseOut = PerformanceOptimizer.debounce(function(params) {
                if (params.componentType === 'geo' || params.seriesType === 'map') {
                    ErrorHandler.safeExecute(function() {
                        self.onUserInteractionEnd();
                    }, self, '鼠标离开事件处理出错');
                }
            }, 150);

            // 绑定优化后的事件
            myChart.on('mouseover', throttledMouseOver);
            myChart.on('mouseout', debouncedMouseOut);

            // 图表容器事件
            myChart.getDom().addEventListener('mouseleave', PerformanceOptimizer.debounce(function() {
                ErrorHandler.safeExecute(function() {
                    self.onUserInteractionEnd();
                }, self, '容器离开事件处理出错');
            }, 200));

            // 窗口焦点事件
            window.addEventListener('blur', function() {
                ErrorHandler.safeExecute(function() {
                    self.onUserInteractionStart();
                }, self, '窗口失焦事件处理出错');
            });

            window.addEventListener('focus', PerformanceOptimizer.debounce(function() {
                ErrorHandler.safeExecute(function() {
                    self.onUserInteractionEnd();
                }, self, '窗口获焦事件处理出错');
            }, 300));

            // 页面可见性变化事件
            if (document.visibilityState !== undefined) {
                document.addEventListener('visibilitychange', function() {
                    ErrorHandler.safeExecute(function() {
                        if (document.hidden) {
                            self.onUserInteractionStart();
                        } else {
                            self.onUserInteractionEnd();
                        }
                    }, self, '页面可见性变化事件处理出错');
                });
            }
        };

        // 启动优化后的轮换管理器
        ErrorHandler.safeExecute(function() {
            MapRotationManager.init();
        }, null, '地图轮换管理器初始化失败');

        // 添加窗口大小变化时的图表自适应
        window.addEventListener('resize', PerformanceOptimizer.debounce(function() {
            ErrorHandler.safeExecute(function() {
                myChart.resize();
            }, null, '图表大小调整失败');
        }, 300));

    })
})